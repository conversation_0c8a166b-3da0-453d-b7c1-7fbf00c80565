GCLOUD_PROJECT_ID = tagtoopartners
GKE_CLUSTER_NAME = identity-mapping
GKE_CLUSTER_ZONE = asia-east1-a
GCR_DOMAIN = asia.gcr.io
IMAGE_NAME = identitymapping

PROD_IMAGE_TAG = prod-latest
GCR_PROD_IMAGE = $(GCR_DOMAIN)/$(GCLOUD_PROJECT_ID)/$(IMAGE_NAME):$(PROD_IMAGE_TAG)

K8S_ROOT = k8s

env:
	gcloud config set project $(GCLOUD_PROJECT_ID)
	gcloud config set compute/zone $(GKE_CLUSTER_ZONE)

cred: env
	gcloud container clusters get-credentials $(GKE_CLUSTER_NAME) --region=asia-east1

# manually deploy: step 1
build-prod-image:
	docker buildx build --platform linux/amd64 --no-cache -f Dockerfile -t $(GCR_PROD_IMAGE) .

pull-prod: env cred
	docker pull $(GCR_PROD_IMAGE)

app-up:
	docker-compose up

app-up-silent:
	docker-compose up -d

app-up-build:
	docker-compose up --build

# manually deploy: step 2
push-prod-image:
	docker push $(GCR_PROD_IMAGE)

# manually deploy: step 3
deploy-prod:
	helm upgrade identity-mapping -f $(K8S_ROOT)/values.prod.yaml $(K8S_ROOT)

test-deploy-prod:
	helm upgrade identity-mapping -f $(K8S_ROOT)/values.prod.yaml $(K8S_ROOT) --dry-run --debug

#  --dry-run --debug

# 備份現有的 TLS 憑證
backup-tls-cert: cred
	@echo "備份現有的 TLS 憑證..."
	kubectl get secret tagtoo-com-tw-ssl -n default -o yaml > tagtoo-com-tw-ssl-backup.yaml
	@if [ $$? -eq 0 ]; then \
		echo "成功備份現有的 TLS 憑證到 tagtoo-com-tw-ssl-backup.yaml"; \
	else \
		echo "警告：無法備份現有的 TLS 憑證，可能不存在或無法訪問"; \
	fi

# 安裝 cert-manager (包含備份憑證步驟)
install-cert-manager: cred backup-tls-cert
	chmod +x $(K8S_ROOT)/scripts/install-cert-manager.sh
	cd $(K8S_ROOT) && ./scripts/install-cert-manager.sh

# 監控憑證獲取狀態
monitor-certificate: cred
	@echo "使用 watch 命令監控憑證狀態 (每 2 秒更新一次，按 Ctrl+C 退出)..."
	@if command -v watch >/dev/null 2>&1; then \
		watch kubectl get certificate -n default; \
	else \
		echo "watch 命令不可用，使用替代方法..."; \
		kubectl get certificate -n default -w; \
	fi

# 監控憑證獲取狀態 (使用原始的 kubectl watch 模式)
monitor-certificate-raw: cred
	@echo "使用 kubectl watch 模式監控憑證狀態 (狀態變化時更新，按 Ctrl+C 退出)..."
	kubectl get certificate -n default -w

# 檢查憑證詳情
check-certificate: cred
	@echo "檢查所有憑證狀態..."
	kubectl get certificate -n default
	@echo "\n檢查 tagtoo-com-tw-ssl-cert-manager 憑證詳情..."
	kubectl describe certificate tagtoo-com-tw-ssl-cert-manager -n default 2>/dev/null || echo "tagtoo-com-tw-ssl-cert-manager 憑證不存在"
	@echo "\n檢查 ttd-cm-pre-certificate 憑證詳情..."
	kubectl describe certificate ttd-cm-pre-certificate -n default 2>/dev/null || echo "ttd-cm-pre-certificate 憑證不存在"
	@echo "\n檢查憑證有效期..."
	kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.data.tls\.crt}' | base64 --decode | openssl x509 -noout -dates || echo "憑證尚未獲取完成，Secret 不存在"
	@echo "\n檢查 Ingress 使用的憑證..."
	kubectl describe ingress identity-mapping-ingress -n default | grep -A5 TLS

# 全面監控憑證獲取過程
monitor-cert-detailed: cred
	@echo "全面監控憑證獲取過程 (每 5 秒更新一次，按 Ctrl+C 退出)..."
	@if command -v watch >/dev/null 2>&1; then \
		watch -n 5 "echo '=== 憑證狀態 ==='; \
		kubectl get certificate -n default; \
		echo '\n=== 憑證請求 ==='; \
		kubectl get certificaterequest -n default; \
		echo '\n=== DNS-01 挑戰 ==='; \
		kubectl get challenges -n default || echo '無挑戰資源'; \
		echo '\n=== 最近事件 ==='; \
		kubectl get events -n default --field-selector involvedObject.kind=Certificate --sort-by='.lastTimestamp' | tail -5"; \
	else \
		echo "watch 命令不可用，請使用以下命令手動檢查:"; \
		echo "kubectl get certificate -n default"; \
		echo "kubectl get certificaterequest -n default"; \
		echo "kubectl get challenges -n default"; \
		echo "kubectl get events -n default --field-selector involvedObject.kind=Certificate"; \
	fi

# 監控 DNS-01 挑戰
monitor-challenge-logs: cred
	@echo "監控 DNS-01 挑戰 (按 Ctrl+C 退出)..."
	@echo "注意：DNS-01 挑戰通過 cert-manager 控制器直接修改 Cloudflare DNS 記錄"
	@echo "正在監控 cert-manager 控制器日誌..."
	@CERT_MANAGER_POD=$$(kubectl get pods -n cert-manager -l app=cert-manager -o name | head -1); \
	if [ -n "$$CERT_MANAGER_POD" ]; then \
		kubectl logs -f -n cert-manager $$CERT_MANAGER_POD | grep -E "dns01|challenge|certificate|acme|cloudflare"; \
	else \
		echo "未找到 cert-manager Pod"; \
	fi

# 測試 Cloudflare DNS-01 挑戰
test-challenge-endpoint: cred
	@echo "測試 Cloudflare DNS-01 挑戰 TXT 記錄..."
	@CHALLENGE=$$(kubectl get challenge -n default -o name 2>/dev/null | head -1); \
	if [ -n "$$CHALLENGE" ]; then \
		DNS_NAME=$$(kubectl get $$CHALLENGE -n default -o jsonpath='{.spec.dnsName}' 2>/dev/null); \
		if [ -n "$$DNS_NAME" ]; then \
			echo "挑戰域名: $$DNS_NAME"; \
			echo "預期 TXT 記錄名稱: _acme-challenge.$$DNS_NAME"; \
			echo "預期 TXT 記錄值: $$(kubectl get $$CHALLENGE -n default -o jsonpath='{.spec.key}' 2>/dev/null)"; \
			echo "\n實際 TXT 記錄:"; \
			nslookup -type=TXT _acme-challenge.$$DNS_NAME || echo "無法查詢 TXT 記錄"; \
		else \
			echo "無法獲取挑戰域名"; \
		fi; \
	else \
		echo "未找到挑戰資源，可能挑戰尚未開始或已經完成"; \
		echo "您可以使用 'make diagnose-cert' 查看更多信息"; \
	fi

# 檢查憑證的詳細時間信息
check-certificate-time: cred
	@echo "===== 憑證時間信息 ====="
	@echo "\n=== 憑證創建和到期時間 ==="
	@echo "tagtoo-com-tw-ssl-cert-manager 憑證:"
	@if kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default &>/dev/null; then \
		echo "創建時間: $$(kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.metadata.creationTimestamp}')"; \
		echo "有效期:"; \
		kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.data.tls\.crt}' | base64 --decode | openssl x509 -noout -dates; \
		echo "\n自動更新信息:"; \
		echo "cert-manager 會在憑證到期前 30 天自動嘗試更新憑證"; \
		EXPIRY=$$(kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.data.tls\.crt}' | base64 --decode | openssl x509 -noout -enddate | cut -d= -f2); \
		EXPIRY_SECONDS=$$(date -j -f "%b %d %H:%M:%S %Y %Z" "$$EXPIRY" +%s 2>/dev/null || date -d "$$EXPIRY" +%s); \
		NOW_SECONDS=$$(date +%s); \
		DAYS_LEFT=$$(( (EXPIRY_SECONDS - NOW_SECONDS) / 86400 )); \
		RENEWAL_DATE=$$(date -j -v-30d -f "%b %d %H:%M:%S %Y %Z" "$$EXPIRY" "+%Y-%m-%d" 2>/dev/null || date -d "$$EXPIRY - 30 days" "+%Y-%m-%d"); \
		echo "預計更新日期: $$RENEWAL_DATE (到期前 30 天)"; \
		echo "距離到期還有: $$DAYS_LEFT 天"; \
		if [ $$DAYS_LEFT -le 30 ]; then \
			echo "注意: 憑證將在 30 天內到期，cert-manager 應該已經開始嘗試更新"; \
		fi; \
	else \
		echo "憑證 Secret 不存在"; \
	fi
	@echo "\n=== 憑證更新歷史 ==="
	@echo "相關事件 (最近 10 條):"
	kubectl get events -n default --sort-by='.lastTimestamp' | grep -E 'certificate|cert-manager|renew' | tail -10

# 診斷憑證問題
diagnose-cert: cred
	@echo "===== Cloudflare DNS-01 憑證診斷 ====="
	@echo "\n=== 所有憑證狀態 ==="
	kubectl get certificate -n default
	@echo "\n=== tagtoo-com-tw-ssl-cert-manager 憑證詳細信息 ==="
	kubectl describe certificate tagtoo-com-tw-ssl-cert-manager -n default 2>/dev/null || echo "tagtoo-com-tw-ssl-cert-manager 憑證不存在"
	@echo "\n=== ttd-cm-pre-certificate 憑證詳細信息 ==="
	kubectl describe certificate ttd-cm-pre-certificate -n default 2>/dev/null || echo "ttd-cm-pre-certificate 憑證不存在"
	@echo "\n=== 憑證請求詳細信息 ==="
	@CERT_REQ=$$(kubectl get certificaterequest -n default -o name | head -1); \
	if [ -n "$$CERT_REQ" ]; then \
		kubectl describe $$CERT_REQ -n default; \
	else \
		echo "未找到憑證請求資源"; \
	fi
	@echo "\n=== DNS-01 挑戰詳細信息 ==="
	@CHALLENGE=$$(kubectl get challenges -n default -o name | head -1); \
	if [ -n "$$CHALLENGE" ]; then \
		kubectl describe $$CHALLENGE -n default; \
		echo "\n=== DNS 記錄檢查 ==="; \
		DNS_NAME=$$(kubectl get $$CHALLENGE -n default -o jsonpath='{.spec.dnsName}' 2>/dev/null); \
		if [ -n "$$DNS_NAME" ]; then \
			echo "檢查 _acme-challenge.$$DNS_NAME 的 TXT 記錄:"; \
			nslookup -type=TXT _acme-challenge.$$DNS_NAME || echo "無法查詢 DNS 記錄"; \
			echo "\n=== Cloudflare API Key 檢查 ==="; \
			echo "檢查 Secret 是否存在:"; \
			kubectl get secret cloudflare-api-key-secret -n cert-manager || echo "Cloudflare API Key Secret 不存在"; \
			echo "\n檢查 cert-manager 日誌中是否有 Cloudflare API 相關問題:"; \
			CERT_MANAGER_POD=$$(kubectl get pods -n cert-manager -l app=cert-manager -o name | head -1); \
			if [ -n "$$CERT_MANAGER_POD" ]; then \
				kubectl logs -n cert-manager $$CERT_MANAGER_POD --tail=50 | grep -i cloudflare || echo "未發現 Cloudflare 相關日誌"; \
			else \
				echo "未找到 cert-manager Pod"; \
			fi; \
		else \
			echo "無法獲取挑戰的域名"; \
		fi; \
	else \
		echo "未找到挑戰資源"; \
	fi
	@echo "\n=== ClusterIssuer 狀態 ==="
	kubectl describe clusterissuer letsencrypt-prod
	@echo "\n=== cert-manager 控制器日誌 (最後 20 行) ==="
	kubectl logs -n cert-manager -l app=cert-manager -c cert-manager --tail=20
	@echo "\n=== cert-manager webhook 日誌 (最後 10 行) ==="
	kubectl logs -n cert-manager -l app=webhook -c webhook --tail=10 || echo "無法獲取 webhook 日誌"
	@echo "\n=== Ingress 配置 ==="
	kubectl get ingress -n default
	@echo "\n=== 相關事件 ==="
	kubectl get events -n default --sort-by='.lastTimestamp' | grep -E 'certificate|challenge|cert-manager|dns' | tail -10

# 部署帶有 cert-manager 的 Identity Mapping（請確保憑證已成功獲取）
deploy-prod-with-cert-manager: cred
	@echo "檢查憑證是否已成功獲取..."
	@if kubectl get certificate ttd-cm-pre-certificate -n default -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' | grep -q "True" || \
	    kubectl get certificate tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.status.conditions[?(@.type=="Ready")].status}' | grep -q "True"; then \
		echo "憑證已成功獲取，開始部署..."; \
		helm upgrade identity-mapping -f $(K8S_ROOT)/values.prod.yaml $(K8S_ROOT); \
	else \
		echo "警告：憑證尚未成功獲取！部署可能導致服務中斷。"; \
		echo "請先運行 'make monitor-certificate' 確認憑證狀態為 Ready 後再部署。"; \
		echo "如果您確定要繼續部署，請運行："; \
		echo "helm upgrade identity-mapping -f $(K8S_ROOT)/values.prod.yaml $(K8S_ROOT)"; \
		exit 1; \
	fi
