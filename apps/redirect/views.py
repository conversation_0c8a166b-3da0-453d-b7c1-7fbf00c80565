from datetime import datetime

from django.http.response import HttpResponse
from django.views.generic import View

from identitymapping.tasks import write_to_BigQuery
from identitymapping.enum_picker import BigQueryEnumPicker

# Create your views here.


enum_picker = BigQueryEnumPicker()


class Test(View):
    def get(self, request, *args, **kwargs):
        return HttpResponse('Hi, {}'.format(datetime.now()))


class TTDRedirect(View):
    """
    http://ttd-cm.tagtoo.com.tw/redirect/?ttd_puid=TAGTOO_USER_ID&ttd_id=TTD_USER_ID
    """
    pixel_image = b'\x47\x49\x46\x38\x39\x61\x01\x00\x01\x00\x80\x00\x00\xff\xff\xff\x00\x00\x00\x21\xf9\x04\x01\x00\x00\x00\x00\x2c\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02\x44\x01\x00\x3b'

    def get(self, request, *args, **kwargs):
        tagtoo_user_id = request.GET['ttd_puid']
        ttd_user_id = request.GET['ttd_id']
        redirect_url = request.get_raw_uri()

        partner_id = '0000'
        big_query_enum = enum_picker.pick_up_enum(partner_id)

        row_to_insert = [{
            'tagtoo_cookie': tagtoo_user_id,
            'ttd_cookie': ttd_user_id,
            'redirect_url': redirect_url,
            'created': datetime.now()
        }]
        write_to_BigQuery.delay(
            data=row_to_insert,
            dataset=big_query_enum.dataset.value,
            table=big_query_enum.table.value
        )

        return HttpResponse(self.pixel_image, content_type='image_gif')
