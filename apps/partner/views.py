from datetime import datetime
import logging
import json

from django.views.generic import View
from django.http import JsonResponse
from django.http.request import QueryDict

from identitymapping.tasks import write_to_BigQuery
from identitymapping.enum_picker import <PERSON>QueryEnumPicker
from identitymapping.enums import StatusEnum


# Create your views here.

enum_picker = BigQueryEnumPicker()
# logger: logging.Logger = logging.getLogger(name='server')
# logger.setLevel(logging.INFO)
# logger.addHandler(logging.StreamHandler())

class PartnerUserIDMapping(View):
    """
    http://ttd-cm.tagtoo.com.tw/prn/uidm/?tuid={TAGTOO_USER_ID}&pid={PARTNER_ID}&puid={PARTNER_USER_ID}&{ARGS}
    e.g. https://ttd-cm.tagtoo.com.tw/prn/uidm/?tuid=123&pid=456&puid=user789&link=LINK&book=BOOK
    """

    fixed_qs_keys = ['pid', 'puid', 'tuid']

    def get(self, request, *args, **kwargs):

        def get_client_ip(request):
            # logger.info('Client IP Header: {}'.format(request.META.get('HTTP_X_FORWARDED_FOR')))
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')
            return ip

        query_dict: QueryDict = request.GET.copy()
        partner_id, partner_user_id, tagtoo_user_id = list(map(
            lambda qsk: query_dict.get(qsk, ''), self.fixed_qs_keys
        ))
        big_query_enum = enum_picker.pick_up_enum(partner_id)
        now = datetime.now()

        if not big_query_enum:
            return JsonResponse({'result': 'Unknown Partner'})
        if big_query_enum.status.value == StatusEnum.stop.value:
            return JsonResponse({'result': 'Stop'})
        # if partner_id == '1004' and now.hour % 3 != 0:
        #     return JsonResponse({'result': 'Feebee'})

        for qsk in self.fixed_qs_keys:
            if query_dict.get(qsk):
                query_dict.pop(qsk)
        dynamic_qs_json = json.dumps(query_dict)

        # TODO: a more elegant way
        if partner_id == '1004':
            row_to_insert = [{
                'tagtoo_user_id': tagtoo_user_id,
                'partner_id': partner_id,
                'partner_user_id': partner_user_id,
                'fbp': request.GET.get('fbp'),
                'fbc': request.GET.get('fbc'),
                'ip': get_client_ip(request),
                'created': now
            }]
        # elif partner_id == '1008':
        #     pass
            # return JsonResponse({'result': 'SKIP'})
        else:
            row_to_insert = [{
                'partner_id': partner_id,
                'partner_user_id': partner_user_id,
                'tagtoo_user_id': tagtoo_user_id,
                'user_ip': get_client_ip(request),
                'created': now,
                'arg': dynamic_qs_json
            }]

        write_to_BigQuery.delay(
            data=row_to_insert,
            dataset=big_query_enum.dataset.value,
            table=big_query_enum.table.value
        )
        return JsonResponse({'result': 'SUCCESS'})
