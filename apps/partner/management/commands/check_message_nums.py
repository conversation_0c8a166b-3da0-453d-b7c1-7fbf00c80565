import logging

from django.core.management.base import BaseCommand
from django.conf import settings
import environ
import pika

from identitymapping.mail import EmailClient


BENCHMARK = 100000
logger = logging.getLogger(name='check-message-nums')
logger.setLevel(logging.WARNING)
#logger.addHandler(logging.StreamHandler())


class Command(BaseCommand):
    help = 'Check if the number of messages in the broker queue exceeds the benchmark'

    def handle(self, *args, **options):
        env = environ.Env()
        pika_conn_params = pika.ConnectionParameters(
            host=env.str('RABBITMQ_HOST'),
            port=5672,
            virtual_host=env.str('RABBITMQ_DEFAULT_VHOST'),
            credentials=pika.credentials.PlainCredentials(
                env.str('RABBITMQ_DEFAULT_USER'), env.str('RABBITMQ_DEFAULT_PASS')
            ),
        )
        connection = pika.BlockingConnection(pika_conn_params)
        channel = connection.channel()
        channel.basic_qos(prefetch_count=1)
        # NOTE: sometimes, message_count returns 0 ...
        queue = channel.queue_declare(queue=settings.CELERY_TASK_DEFAULT_QUEUE, passive=True,)
        #logger.info(f'Queue info: {queue}')

        message_count = queue.method.message_count
        #logger.info(f'Get {message_count} messages')

        if message_count > BENCHMARK:
            logger.warning(f'Benchmark exceeds! Current: {message_count} messages')

            client = EmailClient()
            receivers = client.send(
                title=f'Message number in {settings.CELERY_TASK_DEFAULT_QUEUE} exceeds benchmark!',
                content=f'I found totally {message_count} messages and the benchmark is {BENCHMARK}.'
            )
            logger.warning('Warning email sent to {}'.format(', '.join(receivers)))

        channel.close()
