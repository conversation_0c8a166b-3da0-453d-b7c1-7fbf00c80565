import logging
import json
import sys
import traceback
from typing import List

from django.core.management.base import BaseCommand
from django.conf import settings
import environ
import pika

from identitymapping.bigquery import TagtooBigQuery
from identitymapping.enums import BigQueryTableEnum, BigQueryDatasetEnum
from identitymapping.mail import EmailClient


DEFAULT_AMOUNT = 5000
TABLES: List[str] = BigQueryTableEnum.values()
TASKS = {t:[] for t in TABLES}
INSERT_CHUNK_SIZE = 10000

logger = logging.getLogger(name='large-insert-to-bq')
logger.setLevel(logging.WARNING)
#logger.addHandler(logging.StreamHandler())


def insert_to_bq(table, data):
    #logger.info(f'Inserting to {table} with {len(data)} records')
    client = TagtooBigQuery()
    bq_table = client.service.get_table(
        client.table_id.format(
            dataset=BigQueryDatasetEnum.TAGTOO_AD.value,
            table=table
        )
    )
    result = client.service.insert_rows(bq_table, data)
    #logger.info(f'Insert to {table} result: {result}')
    client.service.close()


class Command(BaseCommand):
    help = 'Large Insert Records'

    def add_arguments(self, parser):
        parser.add_argument(
            '--amount',
            type=int,
            default=DEFAULT_AMOUNT,
            required=False,
            help='How many records to insert this time.',
        )

    def handle(self, *args, **options):
        amount = options.get('amount')
        #logger.info(f'Prepare to insert {amount} records')

        try:
            env = environ.Env()
            pika_conn_params = pika.ConnectionParameters(
                host=env.str('RABBITMQ_HOST'),
                port=5672,
                virtual_host=env.str('RABBITMQ_DEFAULT_VHOST'),
                credentials=pika.credentials.PlainCredentials(
                    env.str('RABBITMQ_DEFAULT_USER'), env.str('RABBITMQ_DEFAULT_PASS')
                ),
            )
            connection = pika.BlockingConnection(pika_conn_params)
            channel = connection.channel()

            for _ in range(amount):
                method_frame, _, body = channel.basic_get(settings.CELERY_TASK_DEFAULT_QUEUE, auto_ack=True)
                if method_frame:
                    try:
                        content = json.loads(body.decode('utf-8'))
                        TASKS[content[1]['table']].append(content[1]['data'][0])
                    except Exception as e:
                        logger.exception(f'Record content parsing error: {e}')

            for table in TASKS.keys():
                if not TASKS[table]:
                    continue

                chunks = [TASKS[table][i:i + INSERT_CHUNK_SIZE] for i in range(0, len(TASKS[table]), INSERT_CHUNK_SIZE)]
                for chunk in chunks:
                    insert_to_bq(table, data=chunk)

            channel.close()
        except Exception as e:
            error_details = {
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc(),
                'sys_info': {
                    'python_version': sys.version,
                    'platform': sys.platform
                }
            }
            logger.error(f'錯誤信息 (Error Message): {error_details}')

            client = EmailClient()
            error_content = (
                f"錯誤類型 (Error Type): {error_details['error_type']}\n"
                f"錯誤信息 (Error Message): {error_details['error_message']}\n"
                f"堆棧追蹤 (Traceback):\n{error_details['traceback']}\n"
                f"Python 版本 (Python Version): {error_details['sys_info']['python_version']}\n"
                f"平台 (Platform): {error_details['sys_info']['platform']}"
            )
            receivers = client.send(
                title=f'large-insert-to-bq 失敗 (FAIL)',
                content=error_content
            )
            logger.warning('Warning email sent to {}'.format(', '.join(receivers)))
