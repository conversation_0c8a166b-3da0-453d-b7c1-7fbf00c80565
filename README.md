# Identity-Mapping

* The address of the server: https://ttd-cm.tagtoo.com.tw/

# Partner Status
| Partner | Partner ID | Status | Exchange Way | GCP Project | BigQuery | Data Retention Period |
| -------- | -------- | -------- | -------- | -------- | -------- | -------- |
| The Trade Desk (TTD) | 0000 | RUNNING | Redirect | gothic-province-823 | tagtooad.ttd_cookie_mapping | 30 Days |
| VPON | 1001 | STOP | Direct | gothic-province-823 | tagtooad.partner_uid_mapping| 180 Days |
| UNKNOWN | 1002 | STOP | X | X | X | X |
| OB | 1003 | STOP | Direct | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |
| Feebee | 1004 | RUNNING | Direct (Special Case) | gothic-province-823 | tagtooad.feebee_id_mapping | 180 Days |
| Super8 | 1005 | RUNNING | Direct | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |
| Tappie | 1006 | RUNNING | Direct | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |
| Rosetta.ai | 1007 | STOP | ? | ? | ? | ? |
| PopIn | 1008 | RUNNING | Direct | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |
| reurl | 1009 | RUNNING | Direct (Special Case) | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |
| 91APP | 1010 | RUNNING | Direct | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |
| VMFive | 1011 | RUNNING | Direct | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |
| Surveycake | 1012 | RUNNING | Direct | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |
| bridgewell | 1013 | RUNNING | Direct | gothic-province-823 | tagtooad.partner_uid_mapping | 180 Days |

* STOP 僅代表停止合作，但仍可能收到 ID Mapping 的資料

# Ways to Exchange User Maping Data with Data Partners

## Redirect Way: `/redirect/?`

### The Trade Desk (TTD) (0000) Mapping Flow
* 由 Muffet 送出 Tagtoo Permanent User ID 資料到 TTD 伺服器，再由 TTD 伺服器戴上 TTD User ID 資料後 "Redirect" 到 Identity-Mapping 伺服器
  * https://github.com/Tagtoo/muffet/blob/master/src/tracker/resolvers/unitrack/tracker.js#L32
  * https://drive.google.com/drive/u/0/folders/1j8ZK04LLAI74w0-Tvn_IyCi_qyncxE1n

## Direct Way: `/prn/uidm/?`

### Default Mapping Flow
* Clients use [user-exchange-client](https://github.com/Tagtoo/user-exchange-client) to collect data and send HTTP `GET` request to remote server
  * `https://ttd-cm.tagtoo.com.tw/prn/uidm/?tuid=TAGTOO_USER_ID&pid=PARTNER_ID&puid=PARTNER_USER_ID`
* Since [user-exchange-client](https://github.com/Tagtoo/user-exchange-client) uses [unitrack](https://github.com/Tagtoo/unitrack) module to generate and collect first-party Tagtoo user ID, this mapping flow stores Tagtoo user ID also.
* Therefore, this flow requires
  * Tagtoo Code
  * Muffet + unitrack token
  * user-exchange-client

### Special Case: Feebee (1004) Mapping Flow
* For now, we provide Feebee with plain JavaScript snippets, which collect user data and send HTTP `GET` request to the remote server
  * `https://ttd-cm.tagtoo.com.tw/prn/uidm/?pid=FEEBEE_PARTNER_ID&puid=FEEBEE_USER_ID&fbp=FB_fbp&fbc=FB_fbc`
  * The plain JavaScript snippets - https://github.com/Tagtoo/Identity-Mapping/issues/20#issue-1404279512
* No first-party Tagtoo user ID is collected.

### Special Case: reurl (1009) Mapping Flow
* We provide customized client side code - https://github.com/Tagtoo/Identity-Mapping/issues/20#issuecomment-1811797172

# BigQuery Data Cleanup Mechanism
* Approach: BigQuery Scheduled Queries

# Deployment

## 容器映像建構與發布

```bash
# 建構 Docker 映像
docker-compose build

# 標記映像
docker tag identitymapping:prod-latest asia.gcr.io/tagtoopartners/identitymapping:prod-latest

# 推送到 GCP 容器倉庫
docker push asia.gcr.io/tagtoopartners/identitymapping:prod-latest
```

可以在 GCP Artifact Registry 中驗證映像是否成功上傳。

## Kubernetes Deployment

使用 Helm 部署到 Kubernetes 集群：

```bash
# Dry Run (測試部署)
helm upgrade --install identity-mapping ./k8s -f ./k8s/values.prod.yaml --dry-run

# 實際部署
helm upgrade --install identity-mapping ./k8s -f ./k8s/values.prod.yaml

# 回滾到先前版本
helm rollback identity-mapping <REVISION_NUMBER>
```

更詳細的部署資訊請參考 [k8s/README.md](./k8s/README.md)。

# 監控

Identity Mapping 服務使用 GKE 集群中的 monitoring namespace 進行監控。該 namespace 中部署了 Prometheus Stack，包括 Prometheus、Alertmanager、Grafana 等組件，提供全面的監控解決方案。

## 監控文檔

- [GKE Monitoring Namespace 使用指南](./docs/monitoring-guide.md) - 詳細介紹如何使用 monitoring namespace 進行 GKE 監控
- [GKE 監控與 GCP 整合指南](./docs/gcp-monitoring-integration.md) - 說明如何將 monitoring namespace 與 GCP 監控服務整合

## 快速訪問監控界面

### Prometheus

```bash
kubectl port-forward svc/prometheus-kube-prometheus-prometheus 9090:9090 -n monitoring
```

然後在瀏覽器中訪問 http://localhost:9090

### Grafana

```bash
kubectl port-forward svc/prometheus-grafana 3000:80 -n monitoring
```

然後在瀏覽器中訪問 http://localhost:3000

登錄憑證：
- 用戶名：admin
- 密碼：prom-operator
