# GKE Monitoring Namespace 使用指南

本文檔提供了關於如何使用 GKE 集群中的 monitoring namespace 進行監控的詳細說明，以及如何將其與 GCP 監控服務整合。

## 目錄

- [概述](#概述)
- [監控架構](#監控架構)
- [訪問監控界面](#訪問監控界面)
- [Prometheus 使用指南](#prometheus-使用指南)
- [Grafana 使用指南](#grafana-使用指南)
- [AlertManager 配置](#alertmanager-配置)
- [與 GCP 監控整合](#與-gcp-監控整合)
- [自定義監控](#自定義監控)
- [故障排除](#故障排除)
- [最佳實踐](#最佳實踐)

## 概述

monitoring namespace 中運行著 Prometheus Stack，包括 Prometheus、Alertmanager、Grafana 和相關的指標收集器。這些工具共同提供了全面的 Kubernetes 集群監控解決方案。

目前部署的組件包括：
- Prometheus：用於收集和存儲指標數據
- Alertmanager：用於管理和發送警報
- Grafana：用於可視化監控數據
- Kube State Metrics：收集 Kubernetes 集群狀態指標
- Node Exporter：收集節點級別的指標

## 監控架構

整個監控系統的架構如下：

1. **數據收集層**：
   - Node Exporter：收集每個節點的系統指標（CPU、內存、磁盤等）
   - Kube State Metrics：收集 Kubernetes 對象的狀態指標
   - 應用程序指標：通過 ServiceMonitor 從應用程序中抓取指標

2. **數據存儲層**：
   - Prometheus：存儲所有收集的時間序列數據

3. **可視化層**：
   - Grafana：提供豐富的可視化界面和儀表板

4. **告警層**：
   - Alertmanager：處理告警規則觸發的告警，並發送通知

## 訪問監控界面

### Prometheus 界面

使用 port-forward 訪問 Prometheus 界面：

```bash
kubectl port-forward svc/prometheus-kube-prometheus-prometheus 9090:9090 -n monitoring
```

然後在瀏覽器中訪問 http://localhost:9090

### Grafana 界面

使用 port-forward 訪問 Grafana 界面：

```bash
kubectl port-forward svc/prometheus-grafana 3000:80 -n monitoring
```

然後在瀏覽器中訪問 http://localhost:3000

登錄憑證：
- 用戶名：admin
- 密碼：prom-operator

## Prometheus 使用指南

### 查詢指標

Prometheus 使用 PromQL 查詢語言。以下是一些常用的查詢示例：

1. 查詢節點 CPU 使用率：
   ```
   100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
   ```

2. 查詢節點內存使用率：
   ```
   100 * (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes))
   ```

3. 查詢 Pod CPU 使用率：
   ```
   sum(rate(container_cpu_usage_seconds_total{container!="", pod=~"identity-mapping.*"}[5m])) by (pod)
   ```

4. 查詢 Pod 內存使用率：
   ```
   sum(container_memory_usage_bytes{container!="", pod=~"identity-mapping.*"}) by (pod)
   ```

### 查看告警規則

在 Prometheus 界面中，點擊 "Alerts" 標籤可以查看所有配置的告警規則及其當前狀態。

### 查看目標

在 Prometheus 界面中，點擊 "Status" -> "Targets" 可以查看所有監控目標及其健康狀態。

## Grafana 使用指南

### 預設儀表板

Grafana 中已經配置了多個預設儀表板，包括：

1. **Kubernetes / Compute Resources / Cluster**：集群資源使用概況
2. **Kubernetes / Compute Resources / Namespace (Pods)**：命名空間資源使用情況
3. **Kubernetes / Compute Resources / Pod**：Pod 資源使用詳情
4. **Node Exporter / Nodes**：節點系統指標
5. **Kubernetes / Networking / Cluster**：集群網絡概況
6. **Kubernetes / Networking / Namespace (Pods)**：命名空間網絡使用情況
7. **Kubernetes / Networking / Pod**：Pod 網絡使用詳情

### 創建自定義儀表板

1. 點擊左側菜單的 "+" 按鈕，然後選擇 "Dashboard"
2. 點擊 "Add new panel"
3. 在查詢編輯器中輸入 PromQL 查詢
4. 配置面板的顯示選項
5. 點擊 "Save" 保存儀表板

### 設置告警

Grafana 也可以設置告警：

1. 編輯一個面板
2. 點擊 "Alert" 標籤
3. 點擊 "Create Alert"
4. 配置告警條件和通知渠道
5. 點擊 "Save" 保存告警

## AlertManager 配置

AlertManager 負責處理 Prometheus 生成的告警，並將其發送到配置的通知渠道。

### 查看當前告警

使用以下命令查看當前活動的告警：

```bash
kubectl port-forward svc/alertmanager-operated 9093:9093 -n monitoring
```

然後在瀏覽器中訪問 http://localhost:9093

### 配置通知渠道

目前，可以配置 AlertManager 將告警發送到以下渠道：

1. **Slack**：通過 Slack Webhook 發送告警
2. **Email**：通過電子郵件發送告警
3. **PagerDuty**：用於嚴重告警的即時通知
4. **Webhook**：發送到自定義的 HTTP 端點

配置示例（Slack）：

```yaml
alertmanager:
  config:
    global:
      resolve_timeout: 5m
      slack_api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK_URL'

    route:
      receiver: 'slack-notifications'
      group_by: ['alertname', 'job']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 12h

    receivers:
    - name: 'slack-notifications'
      slack_configs:
      - channel: '#monitoring-alerts'
        send_resolved: true
        title: '{{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *警報:* {{ .Annotations.summary }}
          *描述:* {{ .Annotations.description }}
          {{ end }}
```

## 與 GCP 監控整合

monitoring namespace 中的 Prometheus 監控系統可以與 GCP 的監控服務整合，實現更全面的監控和更長期的數據存儲。

### GCP Managed Prometheus

GCP 提供了 Managed Service for Prometheus，可以將 Prometheus 數據發送到 GCP 進行長期存儲和分析。

設置步驟：

1. 啟用 GCP Managed Service for Prometheus API：
   ```bash
   gcloud services enable monitoring.googleapis.com
   ```

2. 安裝 GCP Prometheus Collector：
   ```bash
   kubectl apply -f https://raw.githubusercontent.com/GoogleCloudPlatform/prometheus-engine/main/manifests/setup.yaml
   ```

3. 配置 GCP Prometheus Collector：
   ```bash
   kubectl apply -f - <<EOF
   apiVersion: monitoring.googleapis.com/v1
   kind: OperatorConfig
   metadata:
     name: config
     namespace: gmp-system
   spec:
     projectId: tagtoopartners
   EOF
   ```

4. 創建 PodMonitoring 資源，從現有的 Prometheus 抓取數據：
   ```bash
   kubectl apply -f - <<EOF
   apiVersion: monitoring.googleapis.com/v1
   kind: PodMonitoring
   metadata:
     name: prometheus-federation
     namespace: monitoring
   spec:
     selector:
       matchLabels:
         app.kubernetes.io/name: prometheus
     endpoints:
     - port: web
       path: /federate
       interval: 30s
       params:
         match[]:
         - '{__name__=~".+"}'
   EOF
   ```

### Cloud Monitoring 儀表板

在 GCP Cloud Monitoring 中，您可以創建自定義儀表板來可視化從 Prometheus 收集的指標：

1. 在 GCP Console 中，導航到 "Monitoring" > "Dashboards"
2. 點擊 "Create Dashboard"
3. 添加圖表，選擇 "Prometheus Target" 作為指標類型
4. 使用 PromQL 查詢語言配置圖表

### Cloud Monitoring 告警

您還可以在 GCP Cloud Monitoring 中設置基於 Prometheus 指標的告警：

1. 在 GCP Console 中，導航到 "Monitoring" > "Alerting"
2. 點擊 "Create Policy"
3. 選擇 "Prometheus Target" 作為資源類型
4. 使用 PromQL 查詢配置告警條件
5. 配置通知渠道和告警文檔

### 使用 Cloud Logging

GCP Cloud Logging 可以收集 Kubernetes 集群的日誌，並與監控數據關聯：

1. 確保 Cloud Logging 已啟用：
   ```bash
   gcloud services enable logging.googleapis.com
   ```

2. 在 GCP Console 中，導航到 "Logging" > "Logs Explorer"
3. 使用以下查詢查看特定 Pod 的日誌：
   ```
   resource.type="k8s_container"
   resource.labels.namespace_name="default"
   resource.labels.pod_name:"identity-mapping"
   ```

## 自定義監控

### 為應用程序添加 Prometheus 指標

要將您的應用程序與 Prometheus 監控系統集成，需要執行以下步驟：

1. **在應用程序中添加 Prometheus 指標**：
   - 對於 Python 應用程序，可以使用 `prometheus_client` 庫
   - 對於 Java 應用程序，可以使用 `micrometer-registry-prometheus`
   - 對於 Go 應用程序，可以使用 `prometheus/client_golang`

2. **暴露 /metrics 端點**：
   - 確保應用程序在 /metrics 路徑上暴露 Prometheus 格式的指標

3. **創建 ServiceMonitor 資源**：
   ```yaml
   apiVersion: monitoring.coreos.com/v1
   kind: ServiceMonitor
   metadata:
     name: identity-mapping
     namespace: monitoring
     labels:
       release: prometheus
   spec:
     selector:
       matchLabels:
         app.kubernetes.io/name: identity-mapping
     endpoints:
     - port: http
       interval: 30s
       path: /metrics
     namespaceSelector:
       matchNames:
       - default
   ```

### 創建自定義告警規則

您可以創建自定義的 PrometheusRule 資源來定義告警規則：

```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: identity-mapping-custom-alerts
  namespace: monitoring
  labels:
    release: prometheus
spec:
  groups:
  - name: identity-mapping.alerts
    rules:
    - alert: IdentityMappingHighErrorRate
      expr: |
        sum(rate(http_requests_total{job="identity-mapping",status=~"5.."}[5m])) /
        sum(rate(http_requests_total{job="identity-mapping"}[5m])) > 0.05
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High error rate for Identity Mapping service"
        description: "Error rate is above 5% for the last 5 minutes"
```

## 故障排除

### Prometheus 無法抓取指標

如果 Prometheus 無法抓取應用程序的指標，請檢查以下幾點：

1. 確認應用程序是否正確暴露了 /metrics 端點：
   ```bash
   kubectl port-forward svc/your-service 8080:8080
   curl http://localhost:8080/metrics
   ```

2. 檢查 ServiceMonitor 配置是否正確：
   ```bash
   kubectl get servicemonitor -n monitoring
   kubectl describe servicemonitor your-servicemonitor -n monitoring
   ```

3. 檢查 Prometheus 目標狀態：
   ```bash
   kubectl port-forward svc/prometheus-kube-prometheus-prometheus 9090:9090 -n monitoring
   # 然後在瀏覽器中訪問 http://localhost:9090/targets
   ```

### Grafana 無法顯示數據

如果 Grafana 儀表板無法顯示數據，請檢查以下幾點：

1. 確認 Prometheus 數據源是否正確配置：
   - 在 Grafana 中，導航到 "Configuration" > "Data Sources"
   - 檢查 Prometheus 數據源的 URL 是否正確

2. 檢查 PromQL 查詢是否正確：
   - 在 Grafana 中編輯面板
   - 檢查查詢編輯器中的 PromQL 查詢
   - 在 Prometheus 界面中測試相同的查詢

3. 檢查時間範圍設置：
   - 確保選擇了適當的時間範圍
   - 嘗試擴大時間範圍以查看更多數據

### AlertManager 無法發送通知

如果 AlertManager 無法發送告警通知，請檢查以下幾點：

1. 檢查 AlertManager 配置：
   ```bash
   kubectl get secret alertmanager-prometheus-kube-prometheus-alertmanager -n monitoring -o jsonpath='{.data.alertmanager\.yaml}' | base64 --decode
   ```

2. 檢查 AlertManager 日誌：
   ```bash
   kubectl logs -n monitoring -l app=alertmanager
   ```

3. 測試通知渠道：
   - 對於 Slack，確認 Webhook URL 是否正確
   - 對於電子郵件，確認 SMTP 設置是否正確
   - 對於 PagerDuty，確認 API 密鑰是否正確

## 最佳實踐

### 監控策略

1. **多層監控**：
   - 基礎設施層：監控節點、網絡和存儲
   - Kubernetes 層：監控 Pod、Deployment 和 Service
   - 應用程序層：監控應用程序特定的指標和業務指標

2. **告警策略**：
   - 設置適當的閾值，避免過多的誤報
   - 對不同嚴重程度的告警使用不同的通知渠道
   - 為告警添加詳細的描述和解決方案

3. **數據保留**：
   - 配置適當的數據保留期限，平衡存儲需求和歷史數據需求
   - 考慮使用 GCP Managed Prometheus 進行長期數據存儲

### 資源優化

1. **Prometheus 資源配置**：
   - 根據集群大小和指標數量調整 Prometheus 的資源請求和限制
   - 考慮使用 Prometheus 的遠程存儲功能，將數據存儲到外部系統

2. **Grafana 資源配置**：
   - 對於大型集群，增加 Grafana 的資源限制
   - 限制同時活動的儀表板數量，以減少資源消耗

3. **告警管理**：
   - 合理分組告警，減少通知數量
   - 設置適當的靜默期，避免告警風暴

### 安全性

1. **訪問控制**：
   - 為 Prometheus 和 Grafana 配置適當的身份驗證和授權
   - 使用 Kubernetes RBAC 限制對監控資源的訪問

2. **網絡安全**：
   - 避免直接暴露監控服務到公網
   - 使用 Ingress 或 API Gateway 提供安全的外部訪問

3. **敏感數據保護**：
   - 避免在指標和標籤中包含敏感信息
   - 配置適當的數據保留和清理策略
