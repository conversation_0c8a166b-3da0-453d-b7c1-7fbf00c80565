# GKE 監控與 GCP 整合指南

本文檔提供了關於如何將 GKE 集群中的 monitoring namespace 與 GCP 監控服務整合的詳細說明。

## 目錄

- [概述](#概述)
- [GCP 監控服務簡介](#gcp-監控服務簡介)
- [整合方案](#整合方案)
- [GCP Managed Prometheus](#gcp-managed-prometheus)
- [Cloud Monitoring 自定義指標](#cloud-monitoring-自定義指標)
- [Cloud Logging 整合](#cloud-logging-整合)
- [GCP 告警與通知](#gcp-告警與通知)
- [成本優化](#成本優化)
- [最佳實踐](#最佳實踐)

## 概述

GKE 集群中的 monitoring namespace 已經部署了 Prometheus Stack，包括 Prometheus、Alertmanager、Grafana 等組件。這些工具提供了強大的監控功能，但與 GCP 原生監控服務整合可以帶來更多優勢，如長期數據存儲、更豐富的可視化選項和更強大的告警功能。

## GCP 監控服務簡介

GCP 提供了多種監控服務，可以與 GKE 集群整合：

1. **Cloud Monitoring**：
   - 收集、分析和可視化指標數據
   - 提供預設和自定義儀表板
   - 支持基於閾值的告警

2. **Cloud Logging**：
   - 收集、存儲和分析日誌數據
   - 支持結構化和非結構化日誌
   - 提供強大的日誌搜索和過濾功能

3. **GCP Managed Service for Prometheus**：
   - 提供與開源 Prometheus 兼容的 API
   - 支持長期數據存儲
   - 無需管理 Prometheus 服務器

4. **Error Reporting**：
   - 自動分析日誌中的錯誤
   - 對錯誤進行分組和優先級排序
   - 提供錯誤趨勢分析

## 整合方案

根據不同的需求和場景，可以選擇以下整合方案：

### 方案一：完全使用 GCP 監控服務

適用於希望完全利用 GCP 原生服務的場景。

優點：
- 無需管理監控基礎設施
- 與其他 GCP 服務無縫整合
- 支持長期數據存儲

缺點：
- 可能產生額外的 GCP 費用
- 對於某些高級功能，可能不如開源工具靈活

### 方案二：混合使用 GKE monitoring namespace 和 GCP 服務

適用於希望保留現有 Prometheus Stack 同時利用 GCP 服務優勢的場景。

優點：
- 結合了開源工具的靈活性和 GCP 服務的可靠性
- 可以選擇性地將數據發送到 GCP，控制成本
- 提供本地和雲端兩種監控視角

缺點：
- 需要管理兩套監控系統
- 可能存在數據重複

### 方案三：使用 GCP Managed Prometheus

適用於希望保留 Prometheus 兼容性同時減少管理負擔的場景。

優點：
- 保持與 Prometheus 的兼容性
- 減少管理 Prometheus 服務器的負擔
- 支持長期數據存儲

缺點：
- 可能產生額外的 GCP 費用
- 某些 Prometheus 功能可能受限

## GCP Managed Prometheus

GCP Managed Service for Prometheus 是一個完全托管的 Prometheus 兼容服務，可以無縫整合到現有的 monitoring namespace。

### 設置步驟

1. **啟用 GCP Managed Service for Prometheus API**：

```bash
gcloud services enable monitoring.googleapis.com
```

2. **安裝 GCP Prometheus Collector**：

```bash
kubectl apply -f https://raw.githubusercontent.com/GoogleCloudPlatform/prometheus-engine/main/manifests/setup.yaml
```

3. **配置 GCP Prometheus Collector**：

```bash
kubectl apply -f - <<EOF
apiVersion: monitoring.googleapis.com/v1
kind: OperatorConfig
metadata:
  name: config
  namespace: gmp-system
spec:
  projectId: tagtoopartners
EOF
```

4. **創建 PodMonitoring 資源**：

```bash
kubectl apply -f - <<EOF
apiVersion: monitoring.googleapis.com/v1
kind: PodMonitoring
metadata:
  name: prometheus-federation
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: prometheus
  endpoints:
  - port: web
    path: /federate
    interval: 30s
    params:
      match[]:
      - '{__name__=~".+"}'
EOF
```

### 使用 GCP Managed Prometheus

1. **查詢指標**：
   - 在 GCP Console 中，導航到 "Monitoring" > "Metrics Explorer"
   - 選擇 "Prometheus Target" 作為指標類型
   - 使用 PromQL 查詢語言

2. **創建儀表板**：
   - 在 GCP Console 中，導航到 "Monitoring" > "Dashboards"
   - 點擊 "Create Dashboard"
   - 添加基於 Prometheus 指標的圖表

3. **設置告警**：
   - 在 GCP Console 中，導航到 "Monitoring" > "Alerting"
   - 創建基於 Prometheus 指標的告警策略

## Cloud Monitoring 自定義指標

除了使用 GCP Managed Prometheus，您還可以將自定義指標直接發送到 Cloud Monitoring。

### 使用 Prometheus 導出器

1. **安裝 Stackdriver Prometheus 導出器**：

```bash
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install prometheus-to-sd prometheus-community/prometheus-to-stackdriver -n monitoring
```

2. **配置導出器**：

```yaml
prometheus-to-stackdriver:
  stackdriver:
    projectId: tagtoopartners
    serviceAccountSecret: gcp-sa
  prometheus:
    url: http://prometheus-kube-prometheus-prometheus.monitoring.svc:9090
  metrics:
    - prefix: custom.googleapis.com/prometheus
      source: '{__name__=~"node_.*|kube_.*|container_.*"}'
```

### 使用 OpenCensus 或 OpenTelemetry

對於應用程序級別的指標，可以使用 OpenCensus 或 OpenTelemetry 直接發送到 Cloud Monitoring：

1. **在應用程序中集成 OpenCensus**：

```python
# Python 示例
from opencensus.ext.stackdriver import stats_exporter
from opencensus.stats import stats

# 創建 exporter
exporter = stats_exporter.new_stats_exporter(
    project_id='tagtoopartners')

# 註冊 exporter
stats.stats.view_manager.register_exporter(exporter)
```

2. **定義和記錄指標**：

```python
from opencensus.stats import aggregation, measure, view

# 定義指標
m_request_latency = measure.MeasureFloat(
    "request_latency", "The latency of requests", "ms")

# 定義視圖
request_latency_view = view.View(
    "request_latency_distribution",
    "The distribution of request latencies",
    [],
    m_request_latency,
    aggregation.DistributionAggregation(
        [0, 5, 10, 25, 50, 75, 100, 200, 400, 800, 1000, 2000, 4000, 8000]))

# 註冊視圖
stats.stats.view_manager.register_view(request_latency_view)

# 記錄指標
from opencensus.stats import measurement_map

mmap = measurement_map.MeasurementMap()
mmap.measure_float(m_request_latency, 175.5)
mmap.record()
```

## Cloud Logging 整合

GKE 集群已經自動將容器日誌發送到 Cloud Logging，但您可以進一步優化和利用這些日誌。

### 查詢容器日誌

在 GCP Console 中，導航到 "Logging" > "Logs Explorer"，使用以下查詢查看特定 Pod 的日誌：

```
resource.type="k8s_container"
resource.labels.namespace_name="default"
resource.labels.pod_name:"identity-mapping"
```

### 創建日誌基於指標

您可以基於日誌創建自定義指標，用於監控和告警：

1. 在 GCP Console 中，導航到 "Logging" > "Logs-based Metrics"
2. 點擊 "Create Metric"
3. 選擇 "Counter" 或 "Distribution" 類型
4. 定義日誌過濾器，例如：
   ```
   resource.type="k8s_container"
   resource.labels.namespace_name="default"
   resource.labels.pod_name:"identity-mapping"
   textPayload:"ERROR"
   ```
5. 命名指標並保存

### 設置日誌路由

您可以設置日誌路由，將特定的日誌發送到不同的目的地：

1. 在 GCP Console 中，導航到 "Logging" > "Logs Router"
2. 點擊 "Create Sink"
3. 定義過濾器，選擇目的地（如 BigQuery、Cloud Storage 或 Pub/Sub）
4. 命名路由並保存

## GCP 告警與通知

GCP Cloud Monitoring 提供了強大的告警功能，可以基於指標、日誌和正常運行時間檢查創建告警。

### 創建指標告警

1. 在 GCP Console 中，導航到 "Monitoring" > "Alerting"
2. 點擊 "Create Policy"
3. 選擇指標和條件
4. 配置通知渠道
5. 添加文檔和命名策略

### 創建日誌告警

1. 在 GCP Console 中，導航到 "Monitoring" > "Alerting"
2. 點擊 "Create Policy"
3. 選擇 "Logs-based alerting"
4. 定義日誌查詢和條件
5. 配置通知渠道
6. 添加文檔和命名策略

### 配置通知渠道

GCP 支持多種通知渠道：

1. **Email**：發送電子郵件通知
2. **SMS**：發送短信通知（需要配置）
3. **Slack**：通過 Slack 應用程序發送通知
4. **PagerDuty**：集成 PagerDuty 進行事件管理
5. **Webhook**：發送到自定義的 HTTP 端點

配置步驟：

1. 在 GCP Console 中，導航到 "Monitoring" > "Alerting" > "Edit Notification Channels"
2. 點擊相應的通知類型
3. 填寫必要的信息並保存

## 成本優化

使用 GCP 監控服務可能會產生額外的費用，以下是一些成本優化的建議：

### 選擇性導出指標

不是所有的指標都需要導出到 GCP，可以選擇性地只導出重要的指標：

```yaml
metrics:
  - prefix: custom.googleapis.com/prometheus
    source: '{__name__=~"node_cpu_.*|node_memory_.*|kube_pod_container_status_.*"}'
```

### 優化採樣頻率

降低指標採樣頻率可以減少數據點數量：

```yaml
endpoints:
- port: web
  path: /federate
  interval: 60s  # 從 30s 增加到 60s
```

### 設置適當的數據保留期限

在 GCP Managed Prometheus 中設置適當的數據保留期限：

```yaml
spec:
  projectId: tagtoopartners
  storage:
    retentionDuration: 7d  # 保留 7 天數據
```

### 使用日誌排除過濾器

排除不必要的日誌可以減少 Cloud Logging 的費用：

1. 在 GCP Console 中，導航到 "Logging" > "Logs Router"
2. 點擊 "Create Exclusion"
3. 定義要排除的日誌過濾器，例如：
   ```
   resource.type="k8s_container"
   resource.labels.namespace_name="default"
   resource.labels.pod_name:"identity-mapping"
   severity<="INFO"
   ```
4. 命名排除規則並保存

## 最佳實踐

### 監控策略

1. **分層監控**：
   - 使用 GKE monitoring namespace 進行實時監控和告警
   - 使用 GCP 服務進行長期數據存儲和高級分析

2. **指標分類**：
   - 系統指標：使用 GCP Managed Prometheus
   - 業務指標：使用 OpenCensus/OpenTelemetry 直接發送到 Cloud Monitoring

3. **告警策略**：
   - 緊急告警：使用 AlertManager 發送到 Slack 和 PagerDuty
   - 非緊急告警：使用 GCP 告警發送到電子郵件

### 數據管理

1. **數據保留**：
   - 短期數據（7 天內）：保留在 Prometheus 中
   - 長期數據：存儲在 GCP Managed Prometheus 或 BigQuery 中

2. **數據聚合**：
   - 使用 Prometheus 記錄規則預先聚合數據
   - 對於長期存儲的數據，使用較低的精度

3. **日誌管理**：
   - 關鍵日誌：發送到 Cloud Logging 和 Error Reporting
   - 調試日誌：只在需要時臨時啟用

### 成本控制

1. **監控使用情況**：
   - 定期檢查 GCP 計費報告
   - 設置預算告警，防止意外費用

2. **優化數據量**：
   - 減少高基數標籤
   - 降低非關鍵指標的採樣頻率

3. **資源分配**：
   - 為 Prometheus 和 Grafana 分配適當的資源
   - 使用自動擴縮功能，根據負載調整資源
