# reurl (id=1009) 資料流分析文檔

## 概述

reurl (id=1009) 是 Identity-Mapping 系統中的一個特殊案例，使用 Direct 方式進行資料交換。本文檔詳細說明其資料流程、延遲風險點以及監控方案。

## 基本配置

- **Partner ID**: 1009
- **Status**: RUNNING
- **Exchange Way**: Direct (Special Case)
- **BigQuery Table**: `tagtooad.partner_uid_mapping`
- **Data Retention**: 180 Days
- **Client Side Code**: 自定義客戶端程式碼

## 完整資料流程圖

```mermaid
graph TD
    A[reurl Client Side Code] -->|HTTP GET Request| B[Identity-Mapping Server]
    B --> C[PartnerUserIDMapping View]
    C --> D{Partner ID = 1009?}
    D -->|Yes| E[Pick BigQuery Enum]
    E --> F[Create Row Data]
    F --> G[write_to_BigQuery.delay]
    G --> H[RabbitMQ Queue: default-queue]
    H --> I[Celery Worker]
    I --> J[BigQuery insert_rows]
    J --> K[tagtooad.partner_uid_mapping Table]

    L[large_insert_to_bq CronJob] -->|Every 3 minutes| M[RabbitMQ Queue]
    M --> N[Batch Process 100k records]
    N --> O[Chunk Insert to BigQuery]
    O --> K

    style A fill:#e1f5fe
    style K fill:#f3e5f5
    style L fill:#fff3e0
```

## 詳細資料流程

### 1. 即時寫入流程

當 reurl 發送 HTTP 請求時：

```python
# apps/partner/views.py: PartnerUserIDMapping.get()
row_to_insert = [{
    'partner_id': '1009',
    'partner_user_id': partner_user_id,
    'tagtoo_user_id': tagtoo_user_id,
    'user_ip': get_client_ip(request),
    'created': now,
    'arg': dynamic_qs_json
}]

write_to_BigQuery.delay(
    data=row_to_insert,
    dataset='tagtooad',
    table='partner_uid_mapping'
)
```

### 2. Celery 任務處理

```python
# identitymapping/tasks.py
@app.task(ignore_result=True)
def write_to_BigQuery(data, dataset, table):
    client = TagtooBigQuery()
    table = client.service.get_table(
        client.table_id.format(dataset=dataset, table=table)
    )
    result = client.service.insert_rows(table, data)  # Streaming insert
    client.service.close()
    return result
```

### 3. 批次處理流程

**資料來源**：批次處理從 RabbitMQ `default-queue` 佇列中取得任務

```python
# apps/partner/management/commands/large_insert_to_bq.py
# 每 3 分鐘執行一次，處理 100,000 筆記錄

# 1. 連接 RabbitMQ
pika_conn_params = pika.ConnectionParameters(
    host=env.str('RABBITMQ_HOST'),
    virtual_host=env.str('RABBITMQ_DEFAULT_VHOST'),  # 'identitymapping'
    credentials=pika.credentials.PlainCredentials(...)
)
connection = pika.BlockingConnection(pika_conn_params)
channel = connection.channel()

# 2. 從佇列中取得任務 (與 Celery Worker 競爭同一個佇列)
for _ in range(amount):  # amount = 100000
    method_frame, _, body = channel.basic_get(settings.CELERY_TASK_DEFAULT_QUEUE, auto_ack=True)
    if method_frame:
        content = json.loads(body.decode('utf-8'))
        # content[1] 包含 {'table': 'partner_uid_mapping', 'data': [...], 'dataset': 'tagtooad'}
        TASKS[content[1]['table']].append(content[1]['data'][0])

# 3. 分批插入 BigQuery (每批 10,000 筆)
chunks = [TASKS[table][i:i + INSERT_CHUNK_SIZE] for i in range(0, len(TASKS[table]), INSERT_CHUNK_SIZE)]
for chunk in chunks:
    insert_to_bq(table, data=chunk)
```

**關鍵發現**：
- 批次處理直接從 `default-queue` 佇列消費任務
- 使用 `auto_ack=True`，任務被取出後立即確認
- 如果有 Celery Worker 運行，會與批次處理競爭同一個佇列
- 目前因為沒有 Celery Worker，所有任務都由批次處理處理

## 架構發現

### 重要發現：沒有獨立的 Celery Worker

經過調查發現，**目前沒有獨立的 Celery Worker 部署**：

1. **只有 Web 服務部署**：8 個 `identity-mapping` Pod，只運行 Gunicorn web 服務
2. **Celery Worker 配置被註解**：在 `k8s/values.prod.yaml` 中，`workers` 區段被完全註解掉
3. **批次處理正常運行**：`large-insert-to-bq` CronJob 每 3 分鐘執行一次，處理 100,000 筆記錄

### 🔍 Celery Worker 被關閉的詳細調查

**關鍵 Commit 發現**：
- **Commit Hash**: `faf185e511809e9986ea035d651545cdcf3ef024`
- **作者**: hohh0115 <<EMAIL>>
- **日期**: 2024年1月5日 14:37:39 +0800
- **Commit Message**: `feat: add web replica`
- **變更內容**: 將 `workers:` 區段完全註解掉，同時將 web replica 從 1 增加到 8

**Commit 前後脈絡**：
- 在此 commit 之前，系統有完整的 Celery Worker 配置
- 同一個 commit 中，web replica 從 1 增加到 8，並移除了 "先從8砍成1，因為有資源配置問題" 的註解
- **推測原因**: 可能是為了解決資源配置問題，選擇關閉 Celery Worker 並增加 web replica 來處理更多請求

**Worker 配置歷史**：
- 原本配置：1 個 replica，200 並發，gevent pool
- 自動擴展：最小 1 個，最大 3 個 Pod
- 資源配置：CPU 100m-200m，Memory 300Mi-450Mi

### 架構問題分析

```mermaid
graph TD
    A[reurl HTTP Request] --> B[Web Pod 1-8]
    B --> C[write_to_BigQuery.delay]
    C --> D[RabbitMQ Queue]
    D --> E[❌ 沒有 Celery Worker 處理]
    E --> F[large_insert_to_bq CronJob]
    F --> G[批次處理佇列積壓]
    G --> H[BigQuery]

    style E fill:#ffcdd2
    style F fill:#fff3e0
```

## 延遲風險點分析

### 1. 🚨 關鍵問題：沒有 Celery Worker
- **風險**: 即時寫入任務進入 RabbitMQ 佇列後，沒有 Celery Worker 處理
- **影響**: 所有即時寫入任務都依賴批次處理，導致嚴重延遲
- **監控指標**: 佇列積壓、批次處理頻率

### 2. BigQuery Streaming Buffer
- **風險**: BigQuery 的 streaming insert 有 buffer 機制
- **影響**: 資料可能暫時存在 buffer 中，需要 30-90 秒才能查詢到
- **監控指標**: buffer 大小、延遲時間、資料可用性

### 3. 批次處理機制
- **風險**: `large_insert_to_bq` 每 3 分鐘執行一次
- **影響**: 如果佇列積壓，批次處理會處理較舊的資料
- **監控指標**: 批次處理頻率、處理量、執行時間

### 4. RabbitMQ 佇列積壓
- **風險**: 沒有 Celery Worker 處理佇列中的任務
- **影響**: 任務在佇列中積壓，直到批次處理處理
- **監控指標**: 佇列長度、處理速度

## 延遲時間估算

```mermaid
timeline
    title reurl 資料延遲時間軸 (實際情況)
    section 即時寫入 (無 Worker 處理)
        0s : HTTP 請求到達
        1s : 進入 RabbitMQ 佇列
        ❌ : 沒有 Celery Worker 處理
        3min : large_insert_to_bq 執行
        5min : 處理佇列積壓
        8min : 批次插入 BigQuery
        10min : 資料完全可用查詢
    section 延遲原因
        主要延遲 : 沒有 Celery Worker
        次要延遲 : BigQuery Streaming Buffer
        總延遲 : 3-10 分鐘
```

## 調查結果

### BigQuery Streaming Buffer 檢查
- **Buffer 大小**: 9.6MB，包含 71,003 筆記錄
- **延遲時間**: 最舊的記錄時間為 10 分鐘前
- **reurl 資料量**: 過去一小時內有 114,955 筆記錄，是第二大資料來源

### RabbitMQ 佇列檢查
- **佇列狀態**: `default-queue` 目前為空 (0 messages, 0 consumers)
- **批次處理**: 每 3 分鐘執行一次，處理 100,000 筆記錄
- **處理時間**: 約 113 秒完成 (最新執行：large-insert-to-bq-29228235)
- **處理效率**: 平均每秒處理約 885 筆記錄
- **佇列消費**: 批次處理是唯一的消費者，有效清空佇列積壓

### Celery 配置確認
- **Celery 設定**: 正確配置在 `identitymapping/celery.py` 和 `identitymapping/settings.py`
- **RabbitMQ 連接**: 正常連接，使用 `identitymapping` virtual host
- **任務佇列**: 配置為 `default-queue`
- **實際運行**: **沒有獨立的 Celery Worker 部署**

### 部署架構確認
- **Web 服務**: 8 個 Pod 運行 Gunicorn，只處理 HTTP 請求
- **Celery Worker**: **沒有部署**，所有即時寫入任務都依賴批次處理
- **批次處理**: `large-insert-to-bq` CronJob 每 3 分鐘執行，是唯一的消費者

### 關鍵發現

1. **Celery 配置正確**: 所有 Celery 相關設定都在正確的位置
2. **Worker 被刻意關閉**: 2024年1月5日 commit `faf185e` 中被註解掉，可能因資源配置問題
3. **批次處理高效運作**: 每 3 分鐘處理 100,000 筆記錄，113 秒完成，平均 885 筆/秒
4. **佇列競爭機制**: 批次處理與 Celery Worker 競爭同一個 `default-queue` 佇列
5. **延遲根本原因**: 3 分鐘批次間隔 + 沒有即時 Worker = 3-10 分鐘延遲
6. **資料流向確認**:
   - HTTP 請求 → `write_to_BigQuery.delay()` → RabbitMQ `default-queue`
   - 批次處理每 3 分鐘消費佇列 → BigQuery streaming insert

## 監控和診斷指令

### 1. 檢查 BigQuery Streaming Buffer

```bash
# 檢查 streaming buffer 狀態
bq show --format=prettyjson gothic-province-823:tagtooad.partner_uid_mapping

# 檢查 streaming buffer 統計
bq query --use_legacy_sql=false "
SELECT table_id, creation_time, last_modified_time
FROM \`gothic-province-823.tagtooad.__TABLES__\`
WHERE table_id = 'partner_uid_mapping'
"
```

### 2. 檢查批次處理狀態

```bash
# 檢查 CronJob 狀態
kubectl get cronjobs
kubectl describe cronjob large-insert-to-bq

# 檢查批次處理日誌
kubectl logs job/large-insert-to-bq-<job-id>
```

### 3. 檢查部署狀態

```bash
# 檢查是否有 Celery Worker
kubectl get deployments
kubectl get pods -l app=celery-worker

# 檢查 Web 服務狀態
kubectl get pods -l app.kubernetes.io/name=identity-mapping
```

## 修復建議

### 🚨 緊急修復：啟用 Celery Worker

```yaml
# k8s/values.prod.yaml
workers:
  - name: default
    queues: default-queue
    replicasCount: 3  # 增加 worker 數量
    resources:
      requests:
        cpu: 100m
        memory: 300Mi
      limits:
        cpu: 200m
        memory: 450Mi
    nodeSelector:
      application: identity-mapping
      service: web
    celery:
      poolType: gevent
      concurrency: 200
      maxTasksPerChild: 1500
      withoutGossip: true
      withoutMingle: true
    podAutoscaler:
      min: 1
      max: 3
      cpuPercentage: 60
```

### 2. 調整批次處理頻率

```yaml
# k8s/values.prod.yaml
cronjob:
  - name: large-insert-to-bq
    schedule: "*/1 * * * *"  # 改為每分鐘執行
    args:
      - --amount=50000  # 減少每次處理量
```

### 3. 建立延遲監控

```yaml
# 建立監控指標
- 佇列積壓長度
- 任務處理時間
- BigQuery streaming buffer 延遲
- 批次處理執行時間
```

### 4. 優化 BigQuery 插入策略

```python
# 考慮使用 batch insert 而非 streaming insert
# 或調整 streaming insert 的配置
```

## 根本原因分析

**主要延遲原因**：沒有 Celery Worker 處理即時寫入任務

1. **架構缺陷**：所有即時寫入任務都依賴批次處理
2. **延遲時間**：3-10 分鐘（取決於批次處理頻率）
3. **影響範圍**：所有 partner 的資料都會延遲
4. **解決方案**：啟用 Celery Worker 部署

## 最佳實踐

1. **立即修復**: 啟用 Celery Worker 部署
2. **定期監控**: 建立自動化監控，定期檢查延遲狀況
3. **容量規劃**: 根據流量預測調整 Worker 數量
4. **錯誤處理**: 建立完善的錯誤處理和重試機制
5. **文檔更新**: 定期更新本文檔，記錄新的發現和解決方案

## 相關連結

- [Identity-Mapping README](../README.md)
- [reurl 客戶端程式碼](https://github.com/Tagtoo/Identity-Mapping/issues/20#issuecomment-1811797172)
- [BigQuery 文檔](https://cloud.google.com/bigquery/docs/streaming-data)
- [Celery 文檔](https://docs.celeryproject.org/)

---

*最後更新: 2025-07-28*
*維護者: Backend Team*