import smtplib
from email.utils import formataddr
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText


RECEIVERS = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
]


class EmailClient:

    _email_user = '<EMAIL>'
    _email_pwd = 'qwertyuiop2016'

    _gmail_server_host = 'smtp.gmail.com'
    _gmail_server_port = 465

    def send(self, title, content):
        message = self._get_message(
            title=f'[Identity-Mapping]: {title}',
            content=content
        )

        smtpserver = self._get_smtpserver()
        for receiver in RECEIVERS:
            message.replace_header('To', receiver)
            smtpserver.sendmail(message['From'], message['To'], message.as_string())
        smtpserver.quit()

        return RECEIVERS

    def _get_message(self, title, content):
        message = MIMEMultipart()
        email_content = MIMEText(content)
        message['Subject'] = title
        message['From'] = formataddr(('TAGTOO', self._email_user))
        message['To'] = ''
        message.attach(email_content)
        return message

    def _get_smtpserver(self):
        smtpserver = smtplib.SMTP_SSL(
            host=self._gmail_server_host,
            port=self._gmail_server_port
        )
        smtpserver.ehlo()
        smtpserver.login(
            user=self._email_user,
            password=self._email_pwd
        )
        return smtpserver
