from .enums import *


class EnumPicker:
    base_enum = BaseEnum
    pick_up_key = None

    def __init__(self):
        self.CACHE = {}
        self.register()

    def register(self):
        for child in self.base_enum.__subclasses__():
            self.CACHE[
                getattr(child, self.pick_up_key).value
            ] = child

    def pick_up_enum(self, pick_up_value):
        return self.CACHE.get(str(pick_up_value), None)

    def all_enums(self):
        return self.CACHE.values()


class BigQueryEnumPicker(EnumPicker):
    base_enum = BigQueryEnum
    pick_up_key = 'partner_id'
