import os

from google.cloud import bigquery
from google.oauth2 import service_account

LOCATION = os.path.dirname(__file__)


class TagtooBigQuery:
    """
    bigquery in Tagtoo project
    """
    scopes = ["https://www.googleapis.com/auth/cloud-platform"]
    key_file = os.path.realpath(
        os.path.join(
            LOCATION,
            "../identitymapping/credentials/client-secret/tagtoo-bigquery.json"
        )
    )
    table_id = "gothic-province-823.{dataset}.{table}"

    def __init__(self):
        credentials = service_account.Credentials.from_service_account_file(
            self.key_file,
            scopes=["https://www.googleapis.com/auth/cloud-platform"],
        )
        self.service = bigquery.Client(
            credentials=credentials,
            project=credentials.project_id
        )
