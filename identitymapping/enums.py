from enum import Enum
from typing import List


class BaseEnum(str, Enum):
    @classmethod
    def has_value(cls, value):
        return value in cls._value2member_map_

    @classmethod
    def values(cls) -> List[str]:
        return list(cls._value2member_map_)


class BigQueryEnum(BaseEnum):
    pass


class StatusEnum(BaseEnum):
    running = 'RUNNING'
    stop = 'STOP'


class BigQueryDatasetEnum(BaseEnum):
    TAGTOO_AD = 'tagtooad'


class BigQueryTableEnum(BaseEnum):
    TTD_COOKIE_MAPPING = 'ttd_cookie_mapping'
    PARTNER_UID_MAPPING = 'partner_uid_mapping'
    FEEBEE_ID_MAPPING = 'feebee_id_mapping'


class TTDBigQuery(BigQueryEnum):
    partner_id = '0000'
    identity = 'ttd'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.TTD_COOKIE_MAPPING.value
    status = StatusEnum.running.value


class VponBigQuery(BigQueryEnum):
    partner_id = '1001'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.stop.value

# partner_id = 1002, lost, remains UNKNOWN


class OBDesignBigQuery(BigQueryEnum):
    partner_id = '1003'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.stop.value


class FeebeeBigQuery(BigQueryEnum):
    partner_id = '1004'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.FEEBEE_ID_MAPPING.value
    status = StatusEnum.running.value


class Super8BigQUery(BigQueryEnum):
    partner_id = '1005'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.running.value


class TappieBigQUery(BigQueryEnum):
    partner_id = '1006'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.running.value

# 1007 = rosetta.ai

class PopInBigQUery(BigQueryEnum):
    partner_id = '1008'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.running.value


class ReURLBigQUery(BigQueryEnum):
    partner_id = '1009'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.running.value

class App91BigQUery(BigQueryEnum):
    partner_id = '1010'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.running.value

class VMFiveBigQUery(BigQueryEnum):
    partner_id = '1011'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.running.value

class SurveycakeBigQUery(BigQueryEnum):
    partner_id = '1012'
    identity = 'partner'
    dataset = BigQueryDatasetEnum.TAGTOO_AD.value
    table = BigQueryTableEnum.PARTNER_UID_MAPPING.value
    status = StatusEnum.running.value
