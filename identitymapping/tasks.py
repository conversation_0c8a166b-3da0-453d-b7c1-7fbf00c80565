import logging

from identitymapping.celery import app
from identitymapping.bigquery import TagtooBigQuery


@app.task(ignore_result=True)
def write_to_BigQuery(data, dataset, table):
    # logging.info(f"{data}: {dataset}: {table}")
    client = TagtooBigQuery()
    table = client.service.get_table(
        client.table_id.format(
            dataset=dataset,
            table=table
        )
    )
    result = client.service.insert_rows(table, data)
    client.service.close()

    return result
