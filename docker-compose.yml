version: '3.7'

services:
  web: &web
    container_name: identitymapping
    image: identitymapping:prod-latest
    build:
      context: .
      dockerfile: ./Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - .:/srv/work/
    env_file: .env
    command: gunicorn identitymapping.wsgi --workers 4 --worker-class gevent -b 0.0.0.0:8000 --chdir=/srv/work

  rabbitmq:
    container_name: identitymapping-rabbitmq
    image: rabbitmq:3.8-management
    ports:
      - "5672:5672"
      - "15672:15672"
    env_file: .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:15672"]
      interval: 30s
      timeout: 10s
      retries: 5

  celery_worker:
    <<: *web
    container_name: celery_worker
    depends_on:
      - web
      - rabbitmq
    ports:
      - 5555:5555
    command: >
      bash -c "set -o errexit &&
             set -o pipefail &&
             celery -A identitymapping worker -B --loglevel=INFO -c 2 -Q default-queue"
    env_file: .env
