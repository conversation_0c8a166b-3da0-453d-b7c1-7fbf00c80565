# cert-manager 部署指南 (Cloudflare DNS-01 挑戰方式)

本文檔提供了安裝 cert-manager 並配置自動 TLS 憑證更新的詳細步驟，使用 Cloudflare DNS-01 挑戰方式。

## 背景

Identity Mapping 服務需要 HTTPS 憑證以確保安全通信。以前，這些憑證需要手動更新，容易導致過期和服務中斷。通過使用 cert-manager 和 Cloudflare DNS-01 挑戰方式，我們可以自動獲取和更新憑證，提高服務可靠性。

## Cloudflare DNS-01 挑戰方式的優勢

我們選擇使用 Cloudflare DNS-01 挑戰方式，主要有以下優勢：

1. **不依賴公共 IP** - 不需要將域名指向特定 IP，避免了 DNS 和 IP 同步的問題
2. **適用於內部服務** - 即使服務不對外公開，也可以獲取憑證
3. **支持通配符憑證** - DNS-01 挑戰支持通配符憑證（如 *.tagtoo.com.tw）
4. **避免防火牆問題** - 不需要開放 HTTP 端口
5. **更可靠** - 不受負載均衡器、Ingress 配置等問題的影響
6. **Cloudflare 集成** - Cloudflare 提供了穩定的 API 和快速的 DNS 傳播
7. **自動化程度高** - 整個過程完全自動化，無需人工干預

## 部署流程概述

為了確保安全部署並避免服務中斷，我們採用了分階段部署策略：

1. 安裝 cert-manager 並預先獲取憑證
2. 監控憑證獲取狀態，確保成功
3. 檢查憑證詳情和有效期
4. 部署使用新憑證的服務

## 詳細操作步驟

### 前置準備

1. **確認時間窗口**：
   - 選擇流量較低的時段進行操作
   - 確保有足夠的時間處理可能出現的問題

2. **確認當前服務狀態**：
   ```bash
   kubectl get pods -n default
   kubectl get ingress -n default
   ```

### 階段一：安裝 cert-manager 並預先獲取憑證

1. **切換到正確的 GCP 配置**：
   ```bash
   gcloud config configurations activate tagtoopartners
   ```

2. **獲取 GKE 集群憑證**：
   ```bash
   gcloud container clusters get-credentials identity-mapping --region=asia-east1
   ```

3. **設置 Cloudflare DNS-01 挑戰解析器**：

   您需要提供 Cloudflare 帳戶信息以便 cert-manager 可以創建和刪除 DNS 記錄。這些信息可以通過多種方式提供：

   #### Cloudflare 憑證獲取方法：

   1. 登錄到您的 Cloudflare 帳戶 (https://dash.cloudflare.com/)
   2. 確保您有 tagtoo.com.tw 域名的管理權限
   3. 獲取 Global API Key:
      - 點擊右上角的個人資料圖標
      - 選擇 "My Profile"
      - 在左側菜單中選擇 "API Tokens"
      - 在 "Global API Key" 部分，點擊 "View" 並驗證身份
   4. 記下您的 Cloudflare 帳戶電子郵件和 Global API Key

   #### 憑證管理選項：

   我們提供了三種方式來管理 Cloudflare 憑證：

   1. **使用 GCP Secret Manager（推薦）**：
      - 腳本會自動嘗試從 GCP Secret Manager 獲取憑證
      - 如果憑證不存在，腳本會提示您輸入並詢問是否要保存到 Secret Manager
      - 這樣，團隊成員只需要設置一次憑證，後續使用時會自動獲取

   2. **使用環境變量**：
      ```bash
      export CLOUDFLARE_EMAIL="<EMAIL>"  # 替換為您的 Cloudflare 帳戶電子郵件
      export CLOUDFLARE_API_KEY="your-cloudflare-api-key"          # 替換為您的 Cloudflare Global API Key
      ```

   3. **交互式輸入**：
      - 如果未通過環境變量或 Secret Manager 提供憑證，腳本會提示您輸入

   #### 執行腳本：

   ```bash
   # 確保 setup-cloudflare-dns01-solver.sh 腳本可執行
   chmod +x ./k8s/scripts/setup-cloudflare-dns01-solver.sh

   # 執行腳本
   cd ./k8s/scripts && ./setup-cloudflare-dns01-solver.sh
   ```

   此腳本會：
   - 嘗試從 GCP Secret Manager 獲取 Cloudflare 憑證
   - 如果未找到，提示您輸入 Cloudflare 電子郵件和 API Key
   - 詢問是否要將憑證保存到 GCP Secret Manager 以便將來使用
   - 創建包含 Cloudflare API Key 的 Kubernetes Secret
   - 此 Secret 將用於 cert-manager 訪問 Cloudflare API 來創建和刪除 DNS 記錄

   > **安全提示**：請勿將 Cloudflare API Key 提交到版本控制系統中。使用 GCP Secret Manager 是管理敏感信息的安全方式。

4. **安裝 cert-manager 並啟動憑證獲取流程**：

   如果您已經在上一步設置了環境變量，可以直接執行安裝命令。否則，腳本會提示您輸入必要的信息：
   ```bash
   # 執行安裝命令
   make install-cert-manager
   ```

   此命令會：
   - 備份現有的 TLS 憑證
   - 安裝 cert-manager
   - 提示您輸入 Cloudflare 電子郵件和 API Key（如果未通過環境變量提供）
   - 創建使用 DNS-01 挑戰的 ClusterIssuer
   - 啟動預先憑證獲取流程

   注意：與 HTTP-01 挑戰不同，DNS-01 挑戰不需要修改 Ingress 配置，而是通過修改 DNS 記錄來驗證域名所有權。cert-manager 將自動在 DNS 中創建臨時 TXT 記錄，然後 Let's Encrypt 會驗證這些記錄。

### 階段二：監控憑證獲取狀態

1. **監控憑證獲取狀態**：
   ```bash
   make monitor-certificate
   ```

   等待憑證狀態變為 `Ready`。這可能需要 1-5 分鐘時間，取決於 Let's Encrypt 的響應速度。

2. **檢查憑證詳情**：
   ```bash
   make check-certificate
   ```

   確認：
   - 憑證狀態為 `Ready`
   - 憑證有效期正確
   - 域名正確（ttd-cm.tagtoo.com.tw）

### 階段三：部署使用新憑證的服務

1. **先進行 dry run 測試**：
   ```bash
   make test-deploy-prod
   ```

   仔細檢查輸出，確認 Ingress 配置正確。

2. **部署更新**：
   ```bash
   make deploy-prod-with-cert-manager
   ```

   此命令會：
   - 檢查憑證是否已成功獲取
   - 只有在憑證成功獲取後才部署服務
   - 更新 Ingress 使用新的憑證

3. **驗證部署**：
   ```bash
   kubectl get pods -n default
   kubectl describe ingress identity-mapping-ingress -n default
   ```

4. **測試 HTTPS 連接**：
   ```bash
   curl -v https://ttd-cm.tagtoo.com.tw
   ```

   確認 HTTPS 連接正常，檢查憑證信息。

### 階段四：監控和確認

1. **監控服務日誌**：
   ```bash
   kubectl logs -f deployment/identity-mapping -n default
   ```

   檢查是否有任何 TLS 相關錯誤。

2. **持續監控服務健康狀態**（至少 30 分鐘）：
   ```bash
   kubectl get pods -n default
   ```

   定期檢查，確保服務穩定。

## 故障排除

如果在部署過程中遇到問題，請參考以下故障排除步驟：

### 部署時的 ClusterIssuer 衝突問題

如果在執行 `make deploy-prod-with-cert-manager` 時遇到以下錯誤：

```
Error: UPGRADE FAILED: Unable to continue with update: ClusterIssuer "letsencrypt-prod" in namespace "" exists and cannot be imported into the current release: invalid ownership metadata; label validation error: missing key "app.kubernetes.io/managed-by": must be set to "Helm"; annotation validation error: missing key "meta.helm.sh/release-name": must be set to "identity-mapping"; annotation validation error: missing key "meta.helm.sh/release-namespace": must be set to "default"
```

這是因為 ClusterIssuer 已經通過腳本直接創建，而不是通過 Helm 管理的。解決方法：

1. 刪除現有的 ClusterIssuer：
   ```bash
   kubectl delete clusterissuer letsencrypt-prod
   ```

2. 重新執行部署命令：
   ```bash
   make deploy-prod-with-cert-manager
   ```

這將允許 Helm 創建一個新的、由 Helm 管理的 ClusterIssuer 資源。

### 多個 Certificate 資源指向同一個 Secret 的問題

如果您看到類似以下的錯誤信息：

```
Secret was issued for "ttd-cm-pre-certificate". If this message is not transient, you might have two conflicting Certificates pointing to the same secret.
```

這表示有兩個 Certificate 資源指向同一個 Secret，導致衝突。解決方法：

1. 確認哪個 Certificate 是由 Ingress 控制器自動管理的：
   ```bash
   kubectl get certificate -n default
   kubectl describe certificate tagtoo-com-tw-ssl-cert-manager -n default
   ```

2. 修改手動創建的 Certificate 使用不同的 Secret 名稱：
   ```bash
   kubectl patch certificate ttd-cm-pre-certificate -n default -p '{"spec":{"secretName":"ttd-cm-pre-certificate-secret"}}' --type=merge
   ```

3. 刪除並讓 Ingress 控制器重新創建自動管理的 Certificate：
   ```bash
   kubectl delete certificate tagtoo-com-tw-ssl-cert-manager -n default
   ```

4. 確認新的 Certificate 狀態：
   ```bash
   kubectl get certificate -n default
   ```

5. 如果不再需要手動創建的 Certificate，可以安全地刪除它：
   ```bash
   kubectl delete certificate ttd-cm-pre-certificate -n default
   kubectl delete secret ttd-cm-pre-certificate-secret -n default
   ```

### 憑證獲取問題

如果憑證獲取失敗，可以使用我們的診斷命令：

```bash
make diagnose-cert
```

這個命令會顯示所有相關資源的詳細信息和日誌，幫助您快速找出問題所在。

如果您想手動檢查，可以使用以下命令：

1. **檢查 Certificate 資源狀態**：
   ```bash
   kubectl describe certificate ttd-cm-pre-certificate -n default
   ```

   查看 Events 和 Status 部分，了解失敗原因。

2. **檢查 Challenge 資源狀態**：
   ```bash
   kubectl get challenges -n default
   kubectl describe challenges -n default
   ```

   查看 DNS-01 挑戰的狀態和可能的失敗原因。

3. **檢查 DNS TXT 記錄**：
   ```bash
   # 獲取挑戰域名
   CHALLENGE_NAME=$(kubectl get challenges -n default -o name | head -1)
   DNS_NAME=$(kubectl get $CHALLENGE_NAME -n default -o jsonpath='{.spec.dnsName}')

   # 檢查 TXT 記錄
   nslookup -type=TXT _acme-challenge.$DNS_NAME
   ```

   應該返回一個 TXT 記錄，其值與挑戰的 key 值匹配。

4. **檢查 DNS 服務帳戶權限**：
   ```bash
   # 檢查 Secret 是否存在
   kubectl get secret clouddns-dns01-solver-sa -n cert-manager

   # 檢查 cert-manager 日誌中是否有 DNS 權限問題
   kubectl logs -n cert-manager -l app=cert-manager -c cert-manager | grep -i permission
   ```

5. **查看 cert-manager 控制器日誌**：
   ```bash
   kubectl logs -n cert-manager -l app=cert-manager -c cert-manager
   ```

### Cloudflare DNS-01 挑戰的常見問題

1. **Cloudflare API Key 問題**：確保 API Key 有足夠的權限修改 DNS 記錄
2. **Cloudflare Email 配置**：確保使用了正確的 Cloudflare 帳戶 Email
3. **DNS 傳播延遲**：DNS 記錄更改可能需要時間傳播，Let's Encrypt 可能在記錄傳播完成前嘗試驗證
4. **DNS 區域配置錯誤**：確保 Cloudflare 中的 DNS 區域正確配置，並且域名在正確的區域中
5. **API 速率限制**：Cloudflare 有 API 請求速率限制，過多的請求可能導致暫時性失敗
6. **API Key 過期或撤銷**：確保 API Key 未過期或被撤銷
7. **Secret 格式問題**：確保 Secret 中的 API Key 和 Email 格式正確

### 服務切換問題

如果在切換到新憑證後服務出現問題：

1. **檢查 Ingress 配置**：
   ```bash
   kubectl describe ingress identity-mapping-ingress -n default
   ```

   確認 TLS 配置正確。

2. **檢查 Secret 是否存在**：
   ```bash
   kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default
   ```

   確認 Secret 存在且包含 tls.crt 和 tls.key。

## 緊急回滾計劃

如果需要緊急回滾：

1. **切換回舊的手動管理憑證**：
   ```bash
   # 編輯 values.prod.yaml，將 certManager.enabled 設為 false
   sed -i 's/enabled: true/enabled: false/' ./k8s/values.prod.yaml

   # 重新部署
   helm upgrade identity-mapping -f ./k8s/values.prod.yaml ./k8s
   ```

2. **手動恢復舊憑證**：
   ```bash
   kubectl apply -f tagtoo-com-tw-ssl-backup.yaml
   ```

3. **回滾到上一個 Helm 版本**：
   ```bash
   # 查看 Helm 歷史
   helm history identity-mapping

   # 回滾到上一個版本
   helm rollback identity-mapping <版本號>
   ```

## 後續維護

cert-manager 會自動處理憑證更新，通常在憑證到期前 30 天開始嘗試更新。

### 監控憑證狀態

定期檢查憑證狀態：

```bash
kubectl get certificate -n default
kubectl describe certificate tagtoo-com-tw-ssl-cert-manager -n default
```

### 檢查憑證有效期

檢查憑證的有效期：

```bash
kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.data.tls\.crt}' | base64 --decode | openssl x509 -noout -dates
```

### 檢查憑證的詳細時間信息

使用以下命令檢查憑證的創建時間、到期時間和自動更新時間：

```bash
make check-certificate-time
```

這個命令會顯示：
- 憑證的創建時間
- 憑證的有效期（開始和結束日期）
- cert-manager 的自動更新策略（在到期前 30 天自動更新）
- 預計的更新日期
- 距離到期還有多少天
- 憑證更新相關的事件歷史

### 檢查 Ingress 使用的憑證

確認 Ingress 正確使用了 TLS 憑證：

```bash
kubectl describe ingress identity-mapping-ingress -n default | grep -A5 TLS
```

或者直接檢查網站的憑證：

```bash
echo | openssl s_client -servername ttd-cm.tagtoo.com.tw -connect ttd-cm.tagtoo.com.tw:443 2>/dev/null | openssl x509 -noout -dates
```

## 注意事項

1. Let's Encrypt 有速率限制，避免短時間內多次請求憑證
2. 確保 Cloudflare 帳戶和 API Key 有足夠的權限管理 DNS 記錄
3. DNS 記錄更改可能需要時間傳播，但 Cloudflare 通常傳播較快
4. 保留備份的舊憑證，以便在需要時快速回滾
5. 定期檢查 Cloudflare API Key 是否有效
6. 監控 cert-manager 的日誌，及時發現潛在問題
7. 如果需要，可以配置通配符憑證（如 *.tagtoo.com.tw）
8. 確保 Cloudflare 中的 DNS 區域正確配置，並且域名在正確的區域中

## DNS 提供商選擇

本文檔介紹使用 Cloudflare DNS 作為 DNS-01 挑戰解析器的方法。

### 為什麼選擇 Cloudflare DNS？

- Cloudflare DNS 傳播速度快，通常在幾秒鐘內完成
- Cloudflare API 穩定可靠
- 配置相對簡單，只需要 API Key 和電子郵件
- 我們的域名 tagtoo.com.tw 已經在 Cloudflare 上管理

## 更新日誌

- **2025-05-06**：
  - 更新文檔，添加 ClusterIssuer 衝突問題和多個 Certificate 資源指向同一個 Secret 的問題的解決方案
  - 修正了憑證監控命令，使用 `tagtoo-com-tw-ssl-cert-manager` 作為主要 Certificate 資源
  - 移除腳本中的硬編碼敏感信息，改為從 GCP Secret Manager、環境變量或用戶輸入獲取
  - 更新安裝指南，添加有關 GCP Secret Manager 和環境變量的說明
  - 添加了 DNS 提供商選擇的說明，解釋了 Cloudflare DNS 的使用方法
  - 添加了 `check-certificate-time` 命令，用於檢查憑證的創建時間、到期時間和自動更新時間
