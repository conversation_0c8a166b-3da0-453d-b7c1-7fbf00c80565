# cert-manager Helm 配置
installCRDs: true

# 全局配置
global:
  leaderElection:
    namespace: cert-manager

# cert-manager 控制器配置
controller:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi

# webhook 配置
webhook:
  resources:
    requests:
      cpu: 50m
      memory: 64Mi
    limits:
      cpu: 100m
      memory: 128Mi

# cainjector 配置
cainjector:
  resources:
    requests:
      cpu: 50m
      memory: 64Mi
    limits:
      cpu: 100m
      memory: 128Mi
