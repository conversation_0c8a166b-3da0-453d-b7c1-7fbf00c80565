apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  acme:
    # Let's Encrypt 的 ACME 服務器 URL
    server: https://acme-v02.api.letsencrypt.org/directory
    # 用於 ACME 註冊的電子郵件地址
    email: {{ .Values.certManager.email }}
    # 用於存儲 ACME 帳戶私鑰的 Secret 名稱
    privateKeySecretRef:
      name: letsencrypt-prod-account-key
    # 啟用 DNS-01 挑戰提供者
    solvers:
    - dns01:
        cloudflare:
          # Cloudflare API Key 模式
          email: <EMAIL>
          apiKeySecretRef:
            name: cloudflare-api-key-secret
            key: api-key
