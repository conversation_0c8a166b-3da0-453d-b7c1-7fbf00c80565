apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "identitymapping.ingress" . }}
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.ingress" . }}
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    kubernetes.io/ingress.global-static-ip-name: {{ .Values.ingress.staticIPName }}
    nginx.ingress.kubernetes.io/use-regex: "true"
    {{- if .Values.certManager.enabled }}
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    kubernetes.io/ingress.class: "gce"
    {{- end }}
spec:
  rules:
  - host: {{ .Values.ingress.rules.host }}
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: {{ .Values.nameOverride }}
            port:
              number: 8000

  tls:
  {{- if .Values.certManager.enabled }}
  - hosts:
    - {{ .Values.ingress.rules.host }}
    secretName: {{ .Values.certManager.tlsSecretName | default "tagtoo-com-tw-ssl" }}
  {{- else }}
  - secretName: tagtoo-com-tw-ssl
  {{- end }}
