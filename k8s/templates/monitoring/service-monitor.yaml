{{- if .Values.monitoring.enabled -}}
{{- if .Values.monitoring.serviceMonitor.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "identitymapping.name" . }}
  {{- if .Values.monitoring.serviceMonitor.namespace }}
  namespace: {{ .Values.monitoring.serviceMonitor.namespace }}
  {{- end }}
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.monitoring.serviceMonitor.labels }}
    {{- toYaml .Values.monitoring.serviceMonitor.labels | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ template "identitymapping.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  endpoints:
  - port: http
    interval: {{ .Values.monitoring.serviceMonitor.interval | default "30s" }}
    scrapeTimeout: {{ .Values.monitoring.serviceMonitor.scrapeTimeout | default "10s" }}
    path: /metrics
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
{{- end -}}
{{- end -}}