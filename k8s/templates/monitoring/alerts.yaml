{{- if .Values.monitoring.enabled -}}
{{- if .Values.monitoring.alerts.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ template "identitymapping.name" . }}-alerts
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.monitoring.serviceMonitor.labels }}
    {{- toYaml .Values.monitoring.serviceMonitor.labels | nindent 4 }}
    {{- end }}
spec:
  groups:
  - name: {{ template "identitymapping.name" . }}.alerts
    rules:
    - alert: IdentityMappingDiskSpaceWarning
      expr: |
        (
          container_fs_usage_bytes{container!="", pod=~"{{ template "identitymapping.name" . }}.*"} /
          container_fs_limit_bytes{container!="", pod=~"{{ template "identitymapping.name" . }}.*"}
        ) * 100 > {{ .Values.monitoring.alerts.diskSpaceWarning }}
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High disk usage for pod {{ `{{$labels.pod}}` }} ({{ `{{$value}}` }}%)"
        description: "Pod {{ `{{$labels.pod}}` }} has exceeded {{ .Values.monitoring.alerts.diskSpaceWarning }}% disk usage."
        runbook_url: "https://internal.tagtoo.com.tw/runbooks/identity-mapping/disk-space"

    - alert: IdentityMappingDiskSpaceCritical
      expr: |
        (
          container_fs_usage_bytes{container!="", pod=~"{{ template "identitymapping.name" . }}.*"} /
          container_fs_limit_bytes{container!="", pod=~"{{ template "identitymapping.name" . }}.*"}
        ) * 100 > {{ .Values.monitoring.alerts.diskSpaceCritical }}
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: "Critical disk usage for pod {{ `{{$labels.pod}}` }} ({{ `{{$value}}` }}%)"
        description: "Pod {{ `{{$labels.pod}}` }} has exceeded {{ .Values.monitoring.alerts.diskSpaceCritical }}% disk usage."
        runbook_url: "https://internal.tagtoo.com.tw/runbooks/identity-mapping/disk-space"

    - alert: IdentityMappingHighMemoryUsage
      expr: |
        (
          container_memory_usage_bytes{container!="", pod=~"{{ template "identitymapping.name" . }}.*"} /
          container_memory_limits_bytes{container!="", pod=~"{{ template "identitymapping.name" . }}.*"}
        ) * 100 > {{ .Values.monitoring.alerts.memoryWarning }}
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High memory usage for pod {{ `{{$labels.pod}}` }} ({{ `{{$value}}` }}%)"
        description: "Pod {{ `{{$labels.pod}}` }} has exceeded {{ .Values.monitoring.alerts.memoryWarning }}% memory usage."
        runbook_url: "https://internal.tagtoo.com.tw/runbooks/identity-mapping/memory-usage"

    - alert: IdentityMappingCriticalMemoryUsage
      expr: |
        (
          container_memory_usage_bytes{container!="", pod=~"{{ template "identitymapping.name" . }}.*"} /
          container_memory_limits_bytes{container!="", pod=~"{{ template "identitymapping.name" . }}.*"}
        ) * 100 > {{ .Values.monitoring.alerts.memoryCritical }}
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: "Critical memory usage for pod {{ `{{$labels.pod}}` }} ({{ `{{$value}}` }}%)"
        description: "Pod {{ `{{$labels.pod}}` }} has exceeded {{ .Values.monitoring.alerts.memoryCritical }}% memory usage."
        runbook_url: "https://internal.tagtoo.com.tw/runbooks/identity-mapping/memory-usage"

    - alert: IdentityMappingFrequentRestarts
      expr: |
        increase(kube_pod_container_status_restarts_total{pod=~"{{ template "identitymapping.name" . }}.*", container!=""}[1h]) > {{ .Values.monitoring.alerts.restartThreshold }}
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "Container {{ `{{$labels.container}}` }} in pod {{ `{{$labels.pod}}` }} has restarted {{ `{{$value}}` }} times in the last hour"
        description: "Container {{ `{{$labels.container}}` }} in pod {{ `{{$labels.pod}}` }} has restarted {{ `{{$value}}` }} times in the last hour, which is above the threshold of {{ .Values.monitoring.alerts.restartThreshold }} restarts."
        runbook_url: "https://internal.tagtoo.com.tw/runbooks/identity-mapping/frequent-restarts"
{{- end -}}
{{- end -}}