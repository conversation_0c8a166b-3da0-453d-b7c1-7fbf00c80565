apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "identitymapping.name" . }}
spec:
  replicas: {{ .Values.web.replicaCount }}
  selector:
    matchLabels:
      {{- include "identitymapping.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "identitymapping.selectorLabels" . | nindent 8 }}
      annotations:  # For Pod auto roll deployment
        rollme: {{ randAlphaNum 5 | quote }}
    spec:
      {{- if .Values.web.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.web.nodeSelector | indent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.web.image.repository }}:{{ .Values.web.image.tag }}"
          imagePullPolicy: {{ .Values.web.image.pullPolicy }}
          workingDir: {{ .Values.web.workingDir }}
          # command: ["./scripts/gunicorn.sh"]
          command: ["/bin/bash","-c"]
          args:
            - set -o errexit;
              set -o pipefail;
              set -o nounset;
              gunicorn identitymapping.wsgi --workers 3 --worker-class gevent --max-requests 1000 --max-requests-jitter 50 --timeout 30 -b 0.0.0.0:8000
          ports:
            - containerPort: {{ .Values.web.service.targetPort }}
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ template "identitymapping.environment" . }}
            - secretRef:
                name: {{ template "identitymapping.environmentSecret" . }}
          resources:
{{ toYaml .Values.web.resources | indent 12 }}
