apiVersion: v1
kind: Service
metadata:
  name: {{ template "identitymapping.name" . }}
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  ipFamilyPolicy: PreferDualStack
  ipFamilies:
  - IPv4
  - IPv6
  type: {{ .Values.web.service.type }}
  ports:
  - port: {{ .Values.web.service.port }}
    targetPort: {{ .Values.web.service.targetPort }}
    protocol: TCP
    name: http
  selector:
    {{- include "identitymapping.selectorLabels" . | nindent 4 }}
