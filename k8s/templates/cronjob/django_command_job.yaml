{{- $image := printf "%s:%s" .Values.web.image.repository .Values.web.image.tag }}
{{- $pullPolicy := .Values.web.image.pullPolicy }}
{{- $env_name := include "identitymapping.environment" . }}
{{- $env_secret_name := include "identitymapping.environmentSecret" . }}

{{- range $job := .Values.cronjob }}
{{- if $job.enable }}
---

apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $job.name }}
spec:
  schedule: "{{ $job.schedule }}"
  failedJobsHistoryLimit: 3
  successfulJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        spec:
          {{- if $job.nodeSelector }}
          nodeSelector:
{{ toYaml $job.nodeSelector | indent 12 }}
          {{- end }}
          containers:
            - name: {{ $job.name }}
              image: {{ $image }}
              imagePullPolicy: {{ $pullPolicy }}
              {{- with $job.command }}
              command:
{{ toYaml . | indent 16 }}
              {{- end }}
              {{- with $job.args }}
              args:
{{ toYaml . | indent 16 }}
              {{- end }}
              resources:
{{ toYaml $job.resources | indent 16 }}
              envFrom:
                - configMapRef:
                    name: {{ $env_name }}
                - secretRef:
                    name: {{ $env_secret_name }}
          restartPolicy: {{ default "OnFailure" $job.restartPolicy }}
{{- end }}
{{- end }}
