{{- if .Values.logRotation.enabled -}}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ template "identitymapping.name" . }}-log-rotation
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}-log-rotation
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  schedule: {{ .Values.logRotation.schedule | quote }}
  successfulJobsHistoryLimit: {{ .Values.logRotation.successfulJobsHistoryLimit | default 3 }}
  failedJobsHistoryLimit: {{ .Values.logRotation.failedJobsHistoryLimit | default 3 }}
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: {{ template "identitymapping.name" . }}-log-rotator
          containers:
          - name: log-rotator
            image: bitnami/kubectl:latest
            command:
            - /bin/bash
            - -c
            - |
              echo "開始日誌輪轉任務..."
              # 獲取全部 Pod
              PODS=$(kubectl get pods -l app.kubernetes.io/instance={{ .Release.Name }} -o name)
              for POD in $PODS; do
                echo "處理 Pod: $POD"
                # 對每個容器執行日誌清理
                CONTAINERS=$(kubectl get $POD -o jsonpath='{.spec.containers[*].name}')
                for CONTAINER in $CONTAINERS; do
                  echo "清理容器 $CONTAINER 的日誌"
                  # 檢查日誌大小
                  LOG_SIZE=$(kubectl exec $POD -c $CONTAINER -- sh -c "du -sh /var/log/* 2>/dev/null || echo '0'")
                  echo "日誌大小: $LOG_SIZE"

                  # 執行清理 - 壓縮 3 天前的日誌
                  kubectl exec $POD -c $CONTAINER -- sh -c "find /var/log -type f -name '*.log' -mtime +3 -exec gzip {} \; 2>/dev/null || true"

                  # 刪除 7 天前的壓縮日誌
                  kubectl exec $POD -c $CONTAINER -- sh -c "find /var/log -type f -name '*.gz' -mtime +7 -delete 2>/dev/null || true"

                  # 檢查輪轉後的日誌大小
                  LOG_SIZE_AFTER=$(kubectl exec $POD -c $CONTAINER -- sh -c "du -sh /var/log/* 2>/dev/null || echo '0'")
                  echo "清理後日誌大小: $LOG_SIZE_AFTER"
                done
              done

              echo "日誌輪轉任務完成"
            resources:
{{ toYaml .Values.logRotation.resources | indent 14 }}
          restartPolicy: OnFailure
          nodeSelector:
            application: identity-mapping
            service: cronjob
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "identitymapping.name" . }}-log-rotator
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}-log-rotator
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "identitymapping.name" . }}-log-rotator
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}-log-rotator
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
rules:
- apiGroups: [""]
  resources: ["pods", "pods/exec"]
  verbs: ["get", "list", "create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "identitymapping.name" . }}-log-rotator
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}-log-rotator
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
subjects:
- kind: ServiceAccount
  name: {{ template "identitymapping.name" . }}-log-rotator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "identitymapping.name" . }}-log-rotator
{{- end -}}