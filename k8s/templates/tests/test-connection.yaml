apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "identitymapping.fullname" . }}-test-connection"
  labels:
    {{- include "identitymapping.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "identitymapping.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
