apiVersion: v1
kind: ConfigMap
metadata:
    name: {{ template "identitymapping.environment" . }}
    labels:
        app.kubernetes.io/name: {{ template "identitymapping.name" . }}
        helm.sh/chart: {{ include "identitymapping.chart" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
    {{ tuple . (.Values.environment.localPath.config) | include "env.parseFile" | indent 2 }}
