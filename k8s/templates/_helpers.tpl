{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
*/}}
{{- define "identitymapping.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "identitymapping.fullname" -}}
{{- if .Values.fullnameOverride -}}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- $name := default .Chart.Name .Values.nameOverride -}}
{{- if contains $name .Release.Name -}}
{{- .Release.Name | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "identitymapping.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "identitymapping.labels" -}}
helm.sh/chart: {{ include "identitymapping.chart" . }}
{{ include "identitymapping.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "identitymapping.selectorLabels" -}}
app.kubernetes.io/name: {{ include "identitymapping.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Create the name of the service account to use
*/}}
{{- define "identitymapping.serviceAccountName" -}}
{{- if .Values.serviceAccount.create -}}
    {{ default (include "identitymapping.fullname" .) .Values.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "identitymapping.broker" -}}
    {{- printf "%s-rabbitmq" (include "identitymapping.name" .) -}}
{{- end -}}

{{- define "identitymapping.brokerpvc" -}}
    {{- printf "%s-rabbitmq-pvc" (include "identitymapping.name" .) -}}
{{- end -}}

{{- define "identitymapping.worker" -}}
    {{- printf "%s-celery-worker" (include "identitymapping.name" .) -}}
{{- end -}}

{{- define "identitymapping.environment" -}}
    {{- printf "%s-env" (include "identitymapping.name" .) -}}
{{- end -}}

{{- define "identitymapping.environmentSecret" -}}
    {{- printf "%s-env-secret" (include "identitymapping.name" .) -}}
{{- end -}}

{{- define "identitymapping.ingress" -}}
    {{- printf "%s-ingress" (include "identitymapping.name" .) -}}
{{- end -}}

{{/*
Function to parse .env file and output in yaml
KEY_ENV1=VAL_ENV1      KEY_ENV1: VAL_ENV1
KEY_ENV2=VAL_ENV2  =>  KEY_ENV2: VAL_ENV2
KEY_ENV3=VAL_ENV3      KEY_ENV3: VAL_ENV3
Usage:
{{ tuple . "configs/backend/php-fpm/.env" | include "env.parseFile" | indent 2}}
*/}}
{{- define "env.parseFile" -}}
{{- $scope := index . 0 -}}
{{- $filePath := index . 1 -}}
{{- range $scope.Files.Lines $filePath -}}
{{- $a := splitn "=" 2 . -}}
{{- if $a._0 -}}
{{ $a._0 }}: "{{ $a._1 }}"
{{ end -}}
{{- end -}}
{{- end -}}

{{- define "env.parseFileb64" -}}
{{- $scope := index . 0 -}}
{{- $filePath := index . 1 -}}
{{- range $scope.Files.Lines $filePath -}}
{{- $a := splitn "=" 2 . -}}
{{- if $a._0 -}}
{{ $a._0 }}: {{ $a._1 | b64enc }}
{{ end -}}
{{- end -}}
{{- end -}}
