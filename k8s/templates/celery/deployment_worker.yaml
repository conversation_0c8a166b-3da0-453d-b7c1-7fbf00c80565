{{- $image := printf "%s:%s" .Values.web.image.repository .Values.web.image.tag }}
{{- $env_name := include "identitymapping.environment" . }}
{{- $env_secret_name := include "identitymapping.environmentSecret" . }}
{{- $chart := include "identitymapping.chart" . }}
{{- $release_name := .Release.Name }}
{{- $release_service := .Release.Service }}
{{- $worker_name_prefix := include "identitymapping.worker" .}}

{{- range $worker := .Values.workers }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $worker_name_prefix }}-{{ $worker.name }}
  labels:
    app.kubernetes.io/name: {{ $worker_name_prefix }}-{{ $worker.name }}
    helm.sh/chart: {{ $chart }}
    app.kubernetes.io/instance: {{ $release_name }}
    app.kubernetes.io/managed-by: {{ $release_service }}
spec:
  replicas: {{ $worker.replicasCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ $worker_name_prefix }}-{{ $worker.name }}
      app.kubernetes.io/instance: {{ $release_name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ $worker_name_prefix }}-{{ $worker.name }}
        app.kubernetes.io/instance: {{ $release_name }}
    spec:
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      {{- if $worker.nodeSelector }}
      nodeSelector:
{{ toYaml $worker.nodeSelector | indent 8 }}
      {{- end }}
      {{- if $worker.tolerations }}
      tolerations:
{{ toYaml $worker.tolerations | indent 8 }}
      {{- end }}
      containers:
        - name: {{ $worker_name_prefix }}-{{ $worker.name }}
          image: {{ $image }}
          envFrom:
            - configMapRef:
                name: {{ $env_name }}
            - secretRef:
                name: {{ $env_secret_name }}
          command:
            - celery
            - worker
            - --app=identitymapping
            - --loglevel=INFO
            {{- if $worker.celery.maxTasksPerChild }}
            - --max-tasks-per-child={{ $worker.celery.maxTasksPerChild }}
            {{- end }}
            {{- if $worker.celery.poolType }}
            - --pool={{ $worker.celery.poolType }}
            {{- end}}
            {{- if $worker.celery.concurrency }}
            - --concurrency={{ $worker.celery.concurrency }}
            {{- end }}
            {{- if $worker.celery.autoscale }}
            - --autoscale={{ $worker.celery.autoscale }}
            {{- end }}
            {{- if $worker.celery.withoutGossip }}
            - --without-gossip
            {{- end }}
            {{- if $worker.celery.withoutMingle }}
            - --without-mingle
            {{- end }}
            - --queues={{ $worker.queues }}
          resources:
{{ toYaml $worker.resources | indent 12 }}
          lifecycle:
            preStop:
              exec:
                # https://docs.celeryproject.org/en/latest/userguide/workers.html#stopping-the-worker
                command: ["/bin/sh","-c","pkill -9 -f 'celery worker'"]
{{- if $worker.podAutoscaler }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $worker_name_prefix }}-{{ $worker.name }}
  labels:
    app.kubernetes.io/name: {{ $worker_name_prefix }}-{{ $worker.name }}
    helm.sh/chart: {{ $chart }}
    app.kubernetes.io/instance: {{ $release_name }}
    app.kubernetes.io/managed-by: {{ $release_service }}
spec:
  maxReplicas: {{ $worker.podAutoscaler.max }}
  minReplicas: {{ $worker.podAutoscaler.min }}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $worker_name_prefix }}-{{ $worker.name }}
  metrics:
  {{- if $worker.podAutoscaler.memoryPercentage }}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {{ $worker.podAutoscaler.memoryPercentage }}
  {{- end }}
  {{- if $worker.podAutoscaler.cpuPercentage }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ $worker.podAutoscaler.cpuPercentage }}
  {{- end }}
{{- end }}
{{- end }}
