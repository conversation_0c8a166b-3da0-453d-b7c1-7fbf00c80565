apiVersion: v1
kind: PersistentVolume
metadata:
  name: "identity-mapping-rabbitmq-pv"
spec:
  storageClassName: ""
  capacity:
    storage: 10Gi
  accessModes:
    - "ReadWriteOnce"
  gcePersistentDisk:
    pdName: {{ .Values.broker.pdName }}
    fsType: ext4
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ template "identitymapping.brokerpvc" . }}
spec:
  storageClassName: ""
  volumeName: "identity-mapping-rabbitmq-pv"
  accessModes:
    - "ReadWriteOnce"
  resources:
    requests:
      storage: 10Gi
