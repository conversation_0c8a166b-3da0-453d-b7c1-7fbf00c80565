{{- $env_name := include "identitymapping.environment" . }}
{{- $env_secret_name := include "identitymapping.environmentSecret" . }}

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ template "identitymapping.broker" . }}
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.broker" . }}
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  serviceName: "rabbitmq"
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ template "identitymapping.broker" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ template "identitymapping.broker" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      {{- if .Values.broker.nodeSelector }}
      nodeSelector:
{{ toYaml .Values.broker.nodeSelector | indent 8 }}
      {{- end }}
      containers:
        - name: {{ template "identitymapping.broker" . }}
          image: rabbitmq:3.8-management
          envFrom:
            - configMapRef:
                name: {{ $env_name }}
            - secretRef:
                name: {{ $env_secret_name }}
          env:
            - name: RABBITMQ_LOGS
              value: "-"
            - name: RABBITMQ_LOG_BASE
              value: ""
          resources:
{{ toYaml .Values.broker.resources | indent 12 }}
          ports:
          - name: client-access
            containerPort: 5672
          - name: http
            containerPort: 15672
          volumeMounts:
            - name: rabbitmq-volume
              mountPath: /var/lib/rabbitmq
      volumes:
        - name: rabbitmq-volume
          persistentVolumeClaim:
            claimName: {{ template "identitymapping.brokerpvc" . }}
