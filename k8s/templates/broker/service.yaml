apiVersion: v1
kind: Service
metadata:
  name: {{ template "identitymapping.broker" . }}
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.broker" . }}
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: NodePort
  ports:
  - port: 5672
    targetPort: 5672
    protocol: TCP
    name: client-access
  selector:
    app.kubernetes.io/name: {{ template "identitymapping.broker" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
