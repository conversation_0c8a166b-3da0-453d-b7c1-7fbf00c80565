apiVersion: v1
kind: Secret
metadata:
  name: {{ template "identitymapping.environmentSecret" . }}
  labels:
    app.kubernetes.io/name: {{ template "identitymapping.name" . }}
    helm.sh/chart: {{ include "identitymapping.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
{{ tuple . (.Values.environment.localPath.secret) | include "env.parseFileb64" | indent 2}}
