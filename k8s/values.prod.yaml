# Default values for identitymapping.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

name: identity-mapping
nameOverride: "identity-mapping"
fullnameOverride: "IdentityMapping"

workingDir: /srv/work

monitoring:
  enabled: true
  serviceMonitor:
    enabled: false  # 暫時禁用 serviceMonitor，因為必須裝上 django-prometheus 才能使用
    namespace: "monitoring"
    interval: "30s"
    scrapeTimeout: "10s"
    labels:
      release: prometheus
  alerts:
    enabled: true
    diskSpaceWarning: 75
    diskSpaceCritical: 90
    memoryWarning: 85
    memoryCritical: 95
    restartThreshold: 5

web:
  replicaCount: 8
  maxReplicaCount: 12
  image:
    repository: asia.gcr.io/tagtoopartners/identitymapping
    tag: prod-latest
    pullPolicy: Always
  workingDir: /srv/work
  service:
    type: NodePort
    port: 8000
    targetPort: 8000
  nodeSelector:
    application: identity-mapping
    service: web
  resources:
    requests:
      cpu: 300m
      memory: 350Mi
    limits:
      cpu: 500m
      memory: 600Mi

broker:
  pdName: identity-mapping-rabbitmq-pd-prod
  nodeSelector:
    application: identity-mapping
    service: web
  resources:
    requests:
      cpu: 300m
      memory: 600Mi
    limits:
      cpu: 500m
      memory: 800Mi

# workers:
#   - name: default
#     queues: default-queue
#     replicasCount: 1
#     resources:
#       requests:
#         cpu: 100m
#         memory: 300Mi
#       limits:
#         cpu: 200m
#         memory: 450Mi
#     nodeSelector:
#       application: identity-mapping
#       service: web
#     celery:
#       poolType: gevent
#       concurrency: 200
#       maxMemeoryPerChild: 225000
#       maxTasksPerChild: 1500
#       withoutGossip: true
#       withoutMingle: true
#     podAutoscaler:
#       min: 1
#       max: 3
#       cpuPercentage: 60
#     strategy:
#       type: Recreate
#       rollingUpdate: null

cronjob:
  - name: check-message-nums
    nodeSelector:
      application: identity-mapping
      service: cronjob
    enable: true
    schedule: "0 00-16 * * *"
    command:
      - python
      - manage.py
      - check_message_nums
    resources:
      requests:
        cpu: 50m
        memory: 150Mi
      limits:
        cpu: 100m
        memory: 200Mi
  - name: large-insert-to-bq
    nodeSelector:
      application: identity-mapping
      service: cronjob
    enable: true
    schedule: "*/3 * * * *"
    concurrencyPolicy: Forbid
    successfulJobsHistoryLimit: 1  # 只保留 1 個成功的 job
    failedJobsHistoryLimit: 2      # 只保留 2 個失敗的 job
    jobTemplate:
      spec:
        ttlSecondsAfterFinished: 3600  # 一小時後自動清理
    command:
      - python
      - manage.py
      - large_insert_to_bq
    args:
      - --amount=100000
    resources:
      requests:
        cpu: 300m
        memory: 200Mi
      limits:
        cpu: 600m
        memory: 800Mi
    restartPolicy: Never

logRotation:
  enabled: true
  schedule: "0 21 * * *"  # 台灣時間每天早上5點執行 (UTC+8)
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  resources:
    requests:
      cpu: 50m
      memory: 100Mi
    limits:
      cpu: 100m
      memory: 200Mi

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name:

environment:
  localPath:
    config: ".env.prod"
    secret: ".env-secret.prod"

service:
  type: NodePort
  port: 8000
  targetPort: 8000

ingress:
  staticIPName: identity-mapping-ipv4
  rules:
    host: ttd-cm.tagtoo.com.tw

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

affinity: {}

# cert-manager 配置
certManager:
  enabled: true
  email: "<EMAIL>"
  tlsSecretName: "tagtoo-com-tw-ssl-cert-manager"
