#!/bin/bash
set -e

# 確保腳本在錯誤時退出
set -o errexit
set -o pipefail
set -o nounset

echo "開始安裝 cert-manager..."

# 備份步驟已在 Makefile 中處理

# 添加 Jetstack Helm 倉庫
echo "添加 Jetstack Helm 倉庫..."
helm repo add jetstack https://charts.jetstack.io
helm repo update

# 創建 cert-manager 命名空間
echo "創建 cert-manager 命名空間..."
kubectl create namespace cert-manager --dry-run=client -o yaml | kubectl apply -f -

# 安裝 cert-manager
echo "安裝 cert-manager..."
helm upgrade --install cert-manager jetstack/cert-manager \
  --namespace cert-manager \
  --version v1.14.4 \
  --values cert-manager-values.yaml \
  --wait

echo "等待 cert-manager 部署完成..."
kubectl -n cert-manager wait --for=condition=available deployment --all --timeout=300s

# 設置 Cloudflare DNS-01 挑戰解析器
echo "設置 Cloudflare DNS-01 挑戰解析器..."

# 獲取腳本所在目錄的絕對路徑
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
CLOUDFLARE_SCRIPT="${SCRIPT_DIR}/setup-cloudflare-dns01-solver.sh"

# 檢查腳本是否存在
if [ -f "${CLOUDFLARE_SCRIPT}" ]; then
  echo "找到 Cloudflare 設置腳本：${CLOUDFLARE_SCRIPT}"
  chmod +x "${CLOUDFLARE_SCRIPT}"
  "${CLOUDFLARE_SCRIPT}"
else
  # 直接在腳本中創建 Secret
  echo "腳本文件不存在 (${CLOUDFLARE_SCRIPT})，直接創建 Cloudflare API Token Secret..."

  # Cloudflare 配置
  # 嘗試從 GCP Secret Manager 獲取憑證
  if [ -z "${CLOUDFLARE_EMAIL:-}" ] || [ -z "${CLOUDFLARE_API_TOKEN:-}" ]; then
    echo "嘗試從 GCP Secret Manager 獲取 Cloudflare 憑證..."

    # 檢查是否已登錄 GCP 並且可以訪問 Secret Manager
    if gcloud secrets list --limit=1 &>/dev/null; then
      echo "已登錄 GCP 並且可以訪問 Secret Manager"
      # 嘗試獲取 Cloudflare 電子郵件
      if [ -z "${CLOUDFLARE_EMAIL:-}" ]; then
        CLOUDFLARE_EMAIL=$(gcloud secrets versions access latest --secret="cloudflare-email" 2>/dev/null || echo "")
        if [ -n "$CLOUDFLARE_EMAIL" ]; then
          echo "已從 Secret Manager 獲取 Cloudflare 電子郵件"
        fi
      fi

      # 嘗試獲取 Cloudflare API Token
      if [ -z "${CLOUDFLARE_API_TOKEN:-}" ]; then
        CLOUDFLARE_API_TOKEN=$(gcloud secrets versions access latest --secret="cloudflare-api-token" 2>/dev/null || echo "")
        if [ -n "$CLOUDFLARE_API_TOKEN" ]; then
          echo "已從 Secret Manager 獲取 Cloudflare API Token"
        fi
      fi
    fi
  fi

  # 如果仍然沒有獲取到憑證，則提示用戶輸入
  if [ -z "${CLOUDFLARE_EMAIL:-}" ]; then
    read -p "請輸入 Cloudflare 帳戶電子郵件: " CLOUDFLARE_EMAIL

    # 詢問是否要將憑證保存到 Secret Manager
    read -p "是否將 Cloudflare 電子郵件保存到 GCP Secret Manager? (y/n): " SAVE_TO_SECRET
    if [[ "$SAVE_TO_SECRET" =~ ^[Yy]$ ]]; then
      echo -n "$CLOUDFLARE_EMAIL" | gcloud secrets create cloudflare-email --data-file=- || \
      echo -n "$CLOUDFLARE_EMAIL" | gcloud secrets versions add cloudflare-email --data-file=-
      echo "已將 Cloudflare 電子郵件保存到 Secret Manager"
    fi
  fi

  if [ -z "${CLOUDFLARE_API_TOKEN:-}" ]; then
    read -s -p "請輸入 Cloudflare API Token: " CLOUDFLARE_API_TOKEN
    echo

    # 詢問是否要將憑證保存到 Secret Manager
    read -p "是否將 Cloudflare API Token 保存到 GCP Secret Manager? (y/n): " SAVE_TO_SECRET
    if [[ "$SAVE_TO_SECRET" =~ ^[Yy]$ ]]; then
      echo -n "$CLOUDFLARE_API_TOKEN" | gcloud secrets create cloudflare-api-token --data-file=- || \
      echo -n "$CLOUDFLARE_API_TOKEN" | gcloud secrets versions add cloudflare-api-token --data-file=-
      echo "已將 Cloudflare API Token 保存到 Secret Manager"
    fi
  fi

  # 確保必要的變量已設置
  if [ -z "${CLOUDFLARE_EMAIL}" ] || [ -z "${CLOUDFLARE_API_TOKEN}" ]; then
    echo "錯誤：必須提供 Cloudflare 電子郵件和 API Token"
    exit 1
  fi

  # 創建 Kubernetes Secret
  kubectl create namespace cert-manager --dry-run=client -o yaml | kubectl apply -f -

  # 創建包含 Cloudflare API Token 的 Secret
  kubectl create secret generic cloudflare-api-token-secret \
    --namespace=cert-manager \
    --from-literal=api-token=${CLOUDFLARE_API_TOKEN} \
    --dry-run=client -o yaml | kubectl apply -f -

  echo "Cloudflare API Token Secret 創建完成"
fi

# 檢查是否存在由 Helm 管理的 ClusterIssuer
echo "檢查是否存在由 Helm 管理的 ClusterIssuer..."
if kubectl get clusterissuer letsencrypt-prod &> /dev/null; then
  echo "ClusterIssuer 'letsencrypt-prod' 已存在，檢查是否由 Helm 管理..."

  # 檢查是否由 Helm 管理
  if kubectl get clusterissuer letsencrypt-prod -o jsonpath='{.metadata.labels.app\.kubernetes\.io/managed-by}' | grep -q "Helm"; then
    echo "ClusterIssuer 'letsencrypt-prod' 已由 Helm 管理，跳過創建步驟"
  else
    echo "ClusterIssuer 'letsencrypt-prod' 存在但不是由 Helm 管理"
    echo "警告：這可能會導致後續使用 'make deploy-prod-with-cert-manager' 時出現衝突"
    echo "建議：在執行 'make deploy-prod-with-cert-manager' 前先刪除現有的 ClusterIssuer："
    echo "kubectl delete clusterissuer letsencrypt-prod"

    # 創建臨時 ClusterIssuer 用於預先獲取憑證
    echo "創建臨時 ClusterIssuer 'letsencrypt-prod-temp' 用於預先獲取憑證..."
    cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod-temp
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod-temp-account-key
    solvers:
    - dns01:
        cloudflare:
          email: <EMAIL>
          apiKeySecretRef:
            name: cloudflare-api-key-secret
            key: api-key
EOF
  fi
else
  # 創建 ClusterIssuer
  echo "創建 ClusterIssuer..."
  cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod-account-key
    solvers:
    - dns01:
        cloudflare:
          email: <EMAIL>
          apiKeySecretRef:
            name: cloudflare-api-key-secret
            key: api-key
EOF
fi

# 等待 ClusterIssuer 準備就緒
echo "等待 ClusterIssuer 準備就緒..."
sleep 10

# 刪除可能存在的舊 Certificate 資源
echo "刪除可能存在的舊 Certificate 資源..."
kubectl delete certificate ttd-cm-pre-certificate -n default --ignore-not-found=true

# 等待一下，確保舊資源被完全刪除
echo "等待舊資源被完全刪除..."
sleep 10

# 預先獲取憑證
echo "預先獲取憑證（這可能需要幾分鐘時間）..."

# 確定要使用的 ClusterIssuer 名稱
CLUSTER_ISSUER_NAME="letsencrypt-prod"
if kubectl get clusterissuer letsencrypt-prod-temp &> /dev/null; then
  CLUSTER_ISSUER_NAME="letsencrypt-prod-temp"
  echo "使用臨時 ClusterIssuer 'letsencrypt-prod-temp' 獲取憑證..."
fi

# 刪除可能存在的舊 Certificate 資源
echo "刪除可能存在的舊 Certificate 資源..."
kubectl delete certificate ttd-cm-pre-certificate -n default --ignore-not-found=true

# 等待一下，確保舊資源被完全刪除
echo "等待舊資源被完全刪除..."
sleep 5

cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: ttd-cm-pre-certificate
  namespace: default
spec:
  secretName: tagtoo-com-tw-ssl-cert-manager
  issuerRef:
    name: ${CLUSTER_ISSUER_NAME}
    kind: ClusterIssuer
  dnsNames:
  - ttd-cm.tagtoo.com.tw
EOF

echo "注意：DNS-01 挑戰不需要修改 Ingress 配置，而是通過修改 DNS 記錄來驗證域名所有權"
echo "cert-manager 將自動在 DNS 中創建臨時 TXT 記錄，然後 Let's Encrypt 會驗證這些記錄"
echo "請使用以下命令監控 DNS-01 挑戰過程："
echo "  make monitor-certificate    # 監控憑證狀態"
echo "  make monitor-challenge-logs # 監控 cert-manager 日誌"
echo "  make test-challenge-endpoint # 測試 DNS TXT 記錄"

echo "等待憑證獲取完成（這可能需要 1-5 分鐘）..."
echo "您可以使用以下命令監控憑證獲取狀態："
echo "kubectl get certificate ttd-cm-pre-certificate -n default -w"
echo ""
echo "cert-manager 安裝完成！"
echo ""
echo "後續步驟："
echo "1. 監控憑證獲取狀態："
echo "   kubectl get certificate ttd-cm-pre-certificate -n default -w"
echo "2. 確認憑證已成功獲取（狀態為 Ready）："
echo "   kubectl describe certificate ttd-cm-pre-certificate -n default"
echo "3. 檢查憑證有效期："
echo "   kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.data.tls\\.crt}' | base64 --decode | openssl x509 -noout -dates"
echo "4. 部署 Identity Mapping 服務（僅在憑證成功獲取後）："
echo "   make deploy-prod-with-cert-manager"
echo ""
echo "注意事項："
echo "1. 新的 TLS 憑證將存儲在名為 'tagtoo-com-tw-ssl-cert-manager' 的 Secret 中"
echo "2. 原有的 TLS 憑證 'tagtoo-com-tw-ssl' 已備份到 tagtoo-com-tw-ssl-backup.yaml"
echo "3. 請確保在憑證成功獲取後再部署服務，避免服務中斷"
echo "4. 如需恢復舊憑證，可以將 values.prod.yaml 中的 certManager.enabled 設為 false 並重新部署"
echo "5. 或者使用以下命令手動恢復舊憑證："
echo "   kubectl apply -f tagtoo-com-tw-ssl-backup.yaml"
