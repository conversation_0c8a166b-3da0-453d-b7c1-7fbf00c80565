#!/bin/bash
set -e

# 確保腳本在錯誤時退出
set -o errexit
set -o pipefail
set -o nounset

echo "開始安裝 cert-manager..."

# 備份步驟已在 Makefile 中處理

# 添加 Jetstack Helm 倉庫
echo "添加 Jetstack Helm 倉庫..."
helm repo add jetstack https://charts.jetstack.io
helm repo update

# 創建 cert-manager 命名空間
echo "創建 cert-manager 命名空間..."
kubectl create namespace cert-manager --dry-run=client -o yaml | kubectl apply -f -

# 安裝 cert-manager
echo "安裝 cert-manager..."
helm upgrade --install cert-manager jetstack/cert-manager \
  --namespace cert-manager \
  --version v1.14.4 \
  --values cert-manager-values.yaml \
  --wait

echo "等待 cert-manager 部署完成..."
kubectl -n cert-manager wait --for=condition=available deployment --all --timeout=300s

# 設置 Cloudflare DNS-01 挑戰解析器
echo "設置 Cloudflare DNS-01 挑戰解析器..."
chmod +x ./setup-cloudflare-dns01-solver.sh
./setup-cloudflare-dns01-solver.sh

# 創建 ClusterIssuer
echo "創建 ClusterIssuer..."
cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod-account-key
    solvers:
    - dns01:
        cloudflare:
          apiTokenSecretRef:
            name: cloudflare-api-token-secret
            key: api-token
EOF

# 等待 ClusterIssuer 準備就緒
echo "等待 ClusterIssuer 準備就緒..."
sleep 10

# 預先獲取憑證
echo "預先獲取憑證（這可能需要幾分鐘時間）..."
cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: ttd-cm-pre-certificate
  namespace: default
spec:
  secretName: tagtoo-com-tw-ssl-cert-manager
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - ttd-cm.tagtoo.com.tw
EOF

echo "注意：DNS-01 挑戰不需要修改 Ingress 配置，而是通過修改 DNS 記錄來驗證域名所有權"
echo "cert-manager 將自動在 DNS 中創建臨時 TXT 記錄，然後 Let's Encrypt 會驗證這些記錄"

echo "等待憑證獲取完成（這可能需要 1-5 分鐘）..."
echo "您可以使用以下命令監控憑證獲取狀態："
echo "kubectl get certificate ttd-cm-pre-certificate -n default -w"
echo ""
echo "cert-manager 安裝完成！"
echo ""
echo "後續步驟："
echo "1. 監控憑證獲取狀態："
echo "   kubectl get certificate ttd-cm-pre-certificate -n default -w"
echo "2. 確認憑證已成功獲取（狀態為 Ready）："
echo "   kubectl describe certificate ttd-cm-pre-certificate -n default"
echo "3. 檢查憑證有效期："
echo "   kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.data.tls\\.crt}' | base64 --decode | openssl x509 -noout -dates"
echo "4. 部署 Identity Mapping 服務（僅在憑證成功獲取後）："
echo "   make deploy-prod-with-cert-manager"
echo ""
echo "注意事項："
echo "1. 新的 TLS 憑證將存儲在名為 'tagtoo-com-tw-ssl-cert-manager' 的 Secret 中"
echo "2. 原有的 TLS 憑證 'tagtoo-com-tw-ssl' 已備份到 tagtoo-com-tw-ssl-backup.yaml"
echo "3. 請確保在憑證成功獲取後再部署服務，避免服務中斷"
echo "4. 如需恢復舊憑證，可以將 values.prod.yaml 中的 certManager.enabled 設為 false 並重新部署"
echo "5. 或者使用以下命令手動恢復舊憑證："
echo "   kubectl apply -f tagtoo-com-tw-ssl-backup.yaml"
