#!/bin/bash
set -e

# 確保腳本在錯誤時退出
set -o errexit
set -o pipefail
set -o nounset

# Cloudflare 配置
# 嘗試從 GCP Secret Manager 獲取憑證
if [ -z "${CLOUDFLARE_EMAIL:-}" ] || [ -z "${CLOUDFLARE_API_KEY:-}" ]; then
  echo "嘗試從 GCP Secret Manager 獲取 Cloudflare 憑證..."

  # 檢查是否已登錄 GCP 並且可以訪問 Secret Manager
  if gcloud secrets list --limit=1 &>/dev/null; then
    echo "已登錄 GCP 並且可以訪問 Secret Manager"
    # 嘗試獲取 Cloudflare 電子郵件
    if [ -z "${CLOUDFLARE_EMAIL:-}" ]; then
      CLOUDFLARE_EMAIL=$(gcloud secrets versions access latest --secret="cloudflare-email" 2>/dev/null || echo "")
      if [ -n "$CLOUDFLARE_EMAIL" ]; then
        echo "已從 Secret Manager 獲取 Cloudflare 電子郵件"
      fi
    fi

    # 嘗試獲取 Cloudflare API Key
    if [ -z "${CLOUDFLARE_API_KEY:-}" ]; then
      CLOUDFLARE_API_KEY=$(gcloud secrets versions access latest --secret="cloudflare-api-key" 2>/dev/null || echo "")
      if [ -n "$CLOUDFLARE_API_KEY" ]; then
        echo "已從 Secret Manager 獲取 Cloudflare API Key"
      fi
    fi
  fi
fi

# 如果仍然沒有獲取到憑證，則提示用戶輸入
if [ -z "${CLOUDFLARE_EMAIL:-}" ]; then
  read -p "請輸入 Cloudflare 帳戶電子郵件: " CLOUDFLARE_EMAIL

  # 詢問是否要將憑證保存到 Secret Manager
  read -p "是否將 Cloudflare 電子郵件保存到 GCP Secret Manager? (y/n): " SAVE_TO_SECRET
  if [[ "$SAVE_TO_SECRET" =~ ^[Yy]$ ]]; then
    echo -n "$CLOUDFLARE_EMAIL" | gcloud secrets create cloudflare-email --data-file=- || \
    echo -n "$CLOUDFLARE_EMAIL" | gcloud secrets versions add cloudflare-email --data-file=-
    echo "已將 Cloudflare 電子郵件保存到 Secret Manager"
  fi
fi

if [ -z "${CLOUDFLARE_API_KEY:-}" ]; then
  read -s -p "請輸入 Cloudflare API Key: " CLOUDFLARE_API_KEY
  echo

  # 詢問是否要將憑證保存到 Secret Manager
  read -p "是否將 Cloudflare API Key 保存到 GCP Secret Manager? (y/n): " SAVE_TO_SECRET
  if [[ "$SAVE_TO_SECRET" =~ ^[Yy]$ ]]; then
    echo -n "$CLOUDFLARE_API_KEY" | gcloud secrets create cloudflare-api-key --data-file=- || \
    echo -n "$CLOUDFLARE_API_KEY" | gcloud secrets versions add cloudflare-api-key --data-file=-
    echo "已將 Cloudflare API Key 保存到 Secret Manager"
  fi
fi

# 確保必要的變量已設置
if [ -z "${CLOUDFLARE_EMAIL}" ] || [ -z "${CLOUDFLARE_API_KEY}" ]; then
  echo "錯誤：必須提供 Cloudflare 電子郵件和 API Key"
  exit 1
fi

echo "開始設置 Cloudflare DNS-01 挑戰解析器..."

# 創建 Kubernetes Secret
echo "創建 Kubernetes Secret..."
kubectl create namespace cert-manager --dry-run=client -o yaml | kubectl apply -f -

# 創建包含 Cloudflare API Key 的 Secret
kubectl create secret generic cloudflare-api-key-secret \
  --namespace=cert-manager \
  --from-literal=email="${CLOUDFLARE_EMAIL}" \
  --from-literal=api-key="${CLOUDFLARE_API_KEY}" \
  --dry-run=client -o yaml | kubectl apply -f -

echo "Cloudflare DNS-01 挑戰解析器設置完成！"
echo "已創建 Kubernetes Secret 'cloudflare-api-key-secret' 在 cert-manager 命名空間中"
