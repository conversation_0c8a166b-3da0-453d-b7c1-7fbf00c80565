# Identity Mapping Kubernetes 配置

此目錄包含 Identity Mapping 服務的 Kubernetes Helm Chart 配置。

## Helm 部署指南

### 環境準備
```bash
# 確保已安裝 Helm 3
helm version

# 確保 kubectl 已正確設定目標叢集
kubectl config current-context
```

### 部署流程

#### 測試部署 (Dry Run)
```bash
# 在實際部署前進行 dry-run 檢查
helm upgrade --install identity-mapping ./k8s -f ./k8s/values.prod.yaml --dry-run
```

#### 正式部署
```bash
# 部署或更新服務
helm upgrade --install identity-mapping ./k8s -f ./k8s/values.prod.yaml
```

#### 版本回滾
```bash
# 查看部署歷史
helm history identity-mapping

# 回滾到指定版本 (例如版本 39)
helm rollback identity-mapping 39
```

#### 刪除部署
```bash
# 完全移除服務
helm uninstall identity-mapping
```

### 部署後驗證
```bash
# 檢查 Pod 狀態
kubectl get pods -l app.kubernetes.io/instance=identity-mapping

# 查看服務日誌
kubectl logs -f deployment/identity-mapping

# 檢查 HPA 狀態
kubectl get hpa identity-mapping
```

## 監控配置

Identity Mapping 服務使用多層級監控方案，結合了 Prometheus 與 GCP 的優勢。

### Prometheus 監控配置

#### 安裝 Prometheus Operator

首先，需要在集群中安裝 Prometheus Operator：

```bash
# 添加 Prometheus Helm 倉庫
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# 安裝 Prometheus Operator 與 Grafana
helm install prometheus prometheus-community/kube-prometheus-stack -n monitoring --create-namespace
```

#### ServiceMonitor 配置

服務已配置 ServiceMonitor 自動抓取指標：
- 指標抓取間隔：30秒
- 指標路徑：`/metrics`
- 部署在 `monitoring` 命名空間

查看配置：
```bash
kubectl get servicemonitor -n monitoring
```

#### 告警規則說明

已配置以下 PrometheusRule 告警規則：

1. **磁盤使用率告警**
   - 警告閾值：75%（持續 5 分鐘）
   - 嚴重閾值：90%（持續 2 分鐘）

2. **記憶體使用率告警**
   - 警告閾值：85%（持續 5 分鐘）
   - 嚴重閾值：95%（持續 2 分鐘）

3. **容器重啟告警**
   - 閾值：1 小時內超過 5 次重啟（持續 15 分鐘）

### Alertmanager Slack 整合

若要將告警發送到 Slack，需要配置 Alertmanager。創建 `alertmanager-values.yaml` 文件：

```yaml
alertmanager:
  config:
    global:
      resolve_timeout: 5m
      slack_api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK_URL'

    route:
      receiver: 'slack-notifications'
      group_by: ['alertname', 'job']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 12h

      routes:
      - receiver: 'slack-critical'
        match:
          severity: critical
        group_wait: 10s

      - receiver: 'slack-warnings'
        match:
          severity: warning

    receivers:
    - name: 'slack-critical'
      slack_configs:
      - channel: '#identity-mapping-alerts'
        send_resolved: true
        icon_emoji: ':rotating_light:'
        title: '🚨 危急告警：{{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *警報:* {{ .Annotations.summary }}
          *描述:* {{ .Annotations.description }}
          *持續時間:* {{ .StartsAt | since }}
          {{ if .Labels.pod }}*Pod:* {{ .Labels.pod }}{{ end }}
          {{ end }}

    - name: 'slack-warnings'
      slack_configs:
      - channel: '#identity-mapping-alerts'
        send_resolved: true
        icon_emoji: ':warning:'
        title: '⚠️ 警告：{{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *警報:* {{ .Annotations.summary }}
          *描述:* {{ .Annotations.description }}
          *持續時間:* {{ .StartsAt | since }}
          {{ if .Labels.pod }}*Pod:* {{ .Labels.pod }}{{ end }}
          {{ end }}

    - name: 'slack-notifications'
      slack_configs:
      - channel: '#identity-mapping-alerts'
        send_resolved: true
        title: '{{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *警報:* {{ .Annotations.summary }}
          *描述:* {{ .Annotations.description }}
          {{ end }}
```

應用配置：
```bash
helm upgrade prometheus prometheus-community/kube-prometheus-stack \
  -n monitoring -f alertmanager-values.yaml
```

### GCP 監控整合

除了 Prometheus，Identity Mapping 也利用 GCP 原生監控功能：

1. **Cloud Monitoring (Stackdriver)**
   - 自動收集 GKE 集群指標
   - 支持查詢、圖表與儀表板

2. **設置 GCP 告警到 Slack**
   ```bash
   # 首先創建通知通道
   gcloud alpha monitoring channels create \
     --type=slack \
     --display-name="Identity-Mapping-Alerts" \
     --channel-labels=auth-token=xoxb-YOUR_TOKEN,channel-name=identity-mapping-alerts

   # 獲取通道 ID
   CHANNEL_ID=$(gcloud alpha monitoring channels list --format="value(name)" --filter="displayName=Identity-Mapping-Alerts")

   # 創建告警政策，監控磁盤使用率
   gcloud alpha monitoring policies create \
     --display-name="GKE Identity Mapping Disk Usage Alert" \
     --condition="resource.type=k8s_node AND metric.type=\"kubernetes.io/node/disk/percent_used\" > 75" \
     --documentation="Disk usage has exceeded 75% on Identity Mapping nodes" \
     --notification-channels=$CHANNEL_ID
   ```

### Prometheus vs GCP 監控比較

| 功能 | Prometheus + Alertmanager | GCP Monitoring |
|------|--------------------------|----------------|
| 靈活性 | 高（使用 PromQL） | 中（使用 GCP 查詢語言） |
| 配置複雜度 | 較高（YAML 配置） | 較低（UI 配置） |
| 告警渠道 | 多樣（Slack、Email、PagerDuty 等） | 多樣（Slack、Email、SMS 等） |
| 費用 | 免費（自行管理） | 計費（使用 GCP 服務） |
| 適用場景 | 應用層精細監控 | 基礎設施層面監控 |

建議：兩者結合使用，Prometheus 提供應用層監控，GCP 提供基礎設施監控和長期日誌儲存。

## 日誌管理

### 容器日誌輸出配置

所有容器的日誌都配置為輸出到標準輸出/錯誤流：
- Web 服務默認輸出到標準輸出
- RabbitMQ 配置了 `RABBITMQ_LOGS="-"` 環境變量，將日誌重定向到標準輸出

### 自動日誌輪轉

使用 Kubernetes CronJob 進行自動日誌輪轉：
- 執行時間：台灣時間每天早上 5 點（UTC 21:00）
- 執行操作：
  - 壓縮 3 天前的日誌文件 (*.log → *.log.gz)
  - 刪除 7 天前的壓縮日誌文件

設置和管理：
```bash
# 查看日誌輪轉 CronJob 狀態
kubectl get cronjob identity-mapping-log-rotation

# 查看日誌輪轉 Job 執行歷史
kubectl get jobs -l app.kubernetes.io/name=identity-mapping-log-rotation
```

### 日誌管理最佳實踐

1. **GCP Cloud Logging**
   - 所有容器日誌自動收集到 GCP Cloud Logging
   - 在 GCP Console 中查詢和分析日誌

2. **臨時日誌文件管理**
   - 日誌輪轉防止 emptyDir 空間被耗盡
   - 監控告警在磁盤使用率超過 75% 時提前預警

## TLS 憑證自動更新配置

Identity Mapping 服務使用 cert-manager 自動管理 TLS 憑證，確保 HTTPS 連接的安全性和有效性。

### 安全部署流程

為了確保 TLS 憑證更新不會導致服務中斷，我們採用了分階段部署策略：

#### 1. 安裝 cert-manager 並預先獲取憑證

```bash
# 使用 Makefile 命令安裝 cert-manager 並啟動憑證獲取流程
make install-cert-manager

# 或直接執行安裝腳本
chmod +x ./k8s/scripts/install-cert-manager.sh
cd ./k8s && ./scripts/install-cert-manager.sh
```

#### 2. 監控憑證獲取狀態

```bash
# 使用 watch 命令監控憑證狀態（每 2 秒更新一次）
make monitor-certificate

# 使用原始的 kubectl watch 模式監控
make monitor-certificate-raw

# 全面監控憑證獲取過程（顯示憑證、請求、挑戰和事件）
make monitor-cert-detailed

# 診斷憑證問題（顯示所有相關資源的詳細信息和日誌）
make diagnose-cert

# 監控 DNS-01 挑戰（查看 cert-manager 控制器日誌中與 Cloudflare DNS 挑戰相關的部分）
make monitor-challenge-logs

# 測試 DNS-01 挑戰 TXT 記錄是否在 Cloudflare DNS 中正確設置
make test-challenge-endpoint
```

#### 3. 檢查憑證詳情

```bash
# 檢查憑證詳細信息和有效期
make check-certificate

# 或直接使用 kubectl 命令
kubectl describe certificate ttd-cm-pre-certificate -n default
kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default -o jsonpath='{.data.tls\.crt}' | base64 --decode | openssl x509 -noout -dates
```

#### 4. 部署使用新憑證的服務（僅在憑證成功獲取後）

```bash
# 部署服務（會自動檢查憑證是否已成功獲取）
make deploy-prod-with-cert-manager
```

### 憑證配置

cert-manager 使用 Let's Encrypt 作為憑證頒發機構，通過 DNS-01 挑戰方式自動獲取和更新憑證：

1. **ClusterIssuer 配置**
   - 使用 Let's Encrypt 生產環境 ACME 服務器
   - 使用 DNS-01 挑戰方式驗證域名所有權
   - 使用 Cloudflare API Key 訪問 Cloudflare DNS
   - 憑證自動存儲在 Kubernetes Secret 中

2. **DNS-01 挑戰機制**
   - cert-manager 會在 Cloudflare DNS 中創建臨時 TXT 記錄
   - Let's Encrypt 通過驗證這些 TXT 記錄來確認域名所有權
   - 不依賴於公共 IP 和 HTTP 路由
   - 適用於內部服務和通配符憑證
   - 避免了 Ingress 和防火牆配置問題

3. **自動更新機制**
   - cert-manager 會在憑證到期前 30 天自動嘗試更新
   - 更新過程完全自動化，無需人工干預
   - 使用獨立的 Secret 名稱，便於回滾

### 憑證狀態檢查

使用以下命令檢查憑證狀態：

```bash
# 查看 Certificate 資源
kubectl get certificates -n default

# 查看詳細憑證信息
kubectl describe certificate -n default

# 查看 cert-manager 日誌
kubectl logs -n cert-manager -l app=cert-manager -c cert-manager
```

### 故障排除

#### Cloudflare DNS-01 憑證獲取問題

如果使用 Cloudflare DNS-01 挑戰獲取憑證失敗或遇到問題，最簡單的方法是使用診斷命令：

```bash
make diagnose-cert
```

這個命令會顯示所有相關資源的詳細信息和日誌，幫助您快速找出問題所在。它包括：
- 憑證詳細信息
- 憑證請求詳細信息
- Cloudflare DNS-01 挑戰詳細信息
- Cloudflare DNS TXT 記錄檢查
- Cloudflare API Key 檢查
- ClusterIssuer 狀態
- cert-manager 控制器日誌
- 相關事件

如果您想手動檢查特定組件，可以使用以下命令：

1. **確認 cert-manager 組件狀態**：
   ```bash
   kubectl get pods -n cert-manager
   # 所有 Pod 應該處於 Running 狀態
   ```

2. **檢查 Certificate 資源狀態**：
   ```bash
   kubectl describe certificate ttd-cm-pre-certificate -n default
   # 查看 Events 和 Status 部分，了解失敗原因
   ```

3. **檢查 CertificateRequest 資源狀態**：
   ```bash
   kubectl get certificaterequest -n default
   kubectl describe certificaterequest -n default
   ```

4. **檢查 Challenge 資源狀態**（Cloudflare DNS-01 挑戰）：
   ```bash
   kubectl get challenges -n default
   kubectl describe challenges -n default
   # 查看 Events 部分，了解挑戰失敗原因
   ```

5. **檢查 Cloudflare DNS TXT 記錄**：
   ```bash
   # 獲取挑戰域名
   CHALLENGE_NAME=$(kubectl get challenges -n default -o name | head -1)
   DNS_NAME=$(kubectl get $CHALLENGE_NAME -n default -o jsonpath='{.spec.dnsName}')

   # 檢查 TXT 記錄
   nslookup -type=TXT _acme-challenge.$DNS_NAME
   # 應該返回一個 TXT 記錄，其值與挑戰的 key 值匹配
   ```

6. **檢查 ClusterIssuer 狀態**：
   ```bash
   kubectl describe clusterissuer letsencrypt-prod
   # 確認 ACME 帳戶註冊成功和 Cloudflare 配置
   ```

7. **檢查 Cloudflare API Key**：
   ```bash
   # 檢查 Secret 是否存在
   kubectl get secret cloudflare-api-key-secret -n cert-manager

   # 檢查 Secret 內容（不顯示實際值）
   kubectl describe secret cloudflare-api-key-secret -n cert-manager

   # 檢查 cert-manager 日誌中是否有 Cloudflare API 相關問題
   CERT_MANAGER_POD=$(kubectl get pods -n cert-manager -l app=cert-manager -o name | head -1)
   kubectl logs $CERT_MANAGER_POD -n cert-manager | grep -i cloudflare
   ```

8. **查看 cert-manager 控制器日誌**：
   ```bash
   CERT_MANAGER_POD=$(kubectl get pods -n cert-manager -l app=cert-manager -o name | head -1)
   kubectl logs $CERT_MANAGER_POD -n cert-manager
   ```

#### 服務切換問題

如果在切換到新憑證後服務出現問題：

1. **檢查 Ingress 配置**：
   ```bash
   kubectl describe ingress identity-mapping-ingress -n default
   # 確認 TLS 配置正確
   ```

2. **檢查 Secret 是否存在**：
   ```bash
   kubectl get secret tagtoo-com-tw-ssl-cert-manager -n default
   # 確認 Secret 存在且包含 tls.crt 和 tls.key
   ```

3. **測試 HTTPS 連接**：
   ```bash
   curl -v https://ttd-cm.tagtoo.com.tw
   # 檢查 TLS 握手過程和憑證信息
   ```

#### 恢復和回滾選項

如果需要回滾或恢復舊配置：

1. **切換回舊的手動管理憑證**：
   ```bash
   # 編輯 values.prod.yaml，將 certManager.enabled 設為 false
   # 然後重新部署
   helm upgrade identity-mapping -f ./k8s/values.prod.yaml ./k8s
   ```

2. **手動恢復舊憑證**：
   ```bash
   # 應用備份的憑證
   kubectl apply -f tagtoo-com-tw-ssl-backup.yaml

   # 重啟 Ingress（可選，如果需要強制更新）
   kubectl annotate ingress identity-mapping-ingress -n default kubernetes.io/ingress.global-static-ip-name-
   kubectl annotate ingress identity-mapping-ingress -n default kubernetes.io/ingress.global-static-ip-name=identity-mapping-ipv4
   ```

3. **回滾到上一個 Helm 版本**：
   ```bash
   # 查看 Helm 歷史
   helm history identity-mapping

   # 回滾到上一個版本
   helm rollback identity-mapping 1  # 替換為適當的版本號
   ```

4. **重新觸發憑證獲取**：
   ```bash
   # 刪除現有的 Certificate 資源
   kubectl delete certificate ttd-cm-pre-certificate -n default

   # 重新創建 Certificate 資源
   kubectl apply -f - <<EOF
   apiVersion: cert-manager.io/v1
   kind: Certificate
   metadata:
     name: ttd-cm-pre-certificate
     namespace: default
   spec:
     secretName: tagtoo-com-tw-ssl-cert-manager
     issuerRef:
       name: letsencrypt-prod
       kind: ClusterIssuer
     dnsNames:
     - ttd-cm.tagtoo.com.tw
   EOF
   ```

## 最近優化項目 (2025-05-05)

最近進行了以下優化：

1. **TLS 憑證自動更新**
   - 整合 cert-manager 實現 TLS 憑證自動更新
   - 使用 Let's Encrypt 作為憑證頒發機構
   - 消除手動更新憑證的需求

為防止節點臨時存儲空間壓力問題，我們還進行了以下優化：

2. **資源限制調整**
   - 增加 web 部署的記憶體限制 (300Mi → 350Mi 請求, 500Mi → 600Mi 限制)
   - 維持 RabbitMQ 的資源設定 (600Mi 請求, 800Mi 限制)

3. **Gunicorn 優化**
   - 減少 worker 數量 (4 → 3)
   - 添加 max-requests 和 jitter 設定，定期回收 worker
   - 添加 timeout 設定，防止長時間請求堆積

4. **RabbitMQ 日誌優化**
   - 將日誌導向 stdout/stderr，不使用本地文件系統
   - 防止日誌堆積佔用 emptyDir 空間

5. **自動擴縮配置**
   - 添加 HorizontalPodAutoscaler，基於 CPU 和記憶體使用率自動擴展
   - 設置 minReplicas=8, maxReplicas=12
   - 添加縮減穩定窗口 (300 秒)，防止頻繁擴縮

6. **資源配額**
   - 添加命名空間資源配額，防止單個服務過度使用集群資源
   - 限制總 CPU、記憶體和臨時存儲使用量

7. **服務可靠性**
   - 添加 PodDisruptionBudget，確保在節點維護期間服務可用性

## 運維建議

1. **監控與預警**
   - 監控節點磁盤使用率，設置 75% 預警閾值
   - 監控 Pod 重啟次數，設置警報
   - 使用 Prometheus + Grafana 或 GCP Monitoring 進行監控

2. **定期維護**
   - 每月檢查節點資源使用情況
   - 定期執行 `kubectl get nodes -o wide` 檢查節點狀態
   - 查看 `kubectl top nodes` 和 `kubectl top pods` 資源使用情況

3. **日誌管理**
   - 使用 GCP Stackdriver 收集和分析日誌
   - 關注 OOM 事件和容器重啟事件
   - 每日自動輪轉日誌避免磁盤佔用過多

## 其他說明

目前服務已遷移到新的 web-pool-new 節點池，這些節點有更大的臨時存儲空間。上述優化措施有助於確保服務的穩定性和可靠性。